<?php
/**
 * Data Management Menu Component
 * Can be included in other pages
 */
?>

<style>
.data-menu-component {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    padding: 25px;
    margin: 20px 0;
}

.data-menu-title {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 20px;
    text-align: center;
}

.data-menu-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 15px;
}

.data-menu-item {
    background: white;
    border-radius: 10px;
    padding: 20px 15px;
    text-decoration: none;
    color: inherit;
    transition: all 0.3s ease;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
    border: 1px solid #e9ecef;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.data-menu-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.data-menu-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
    text-decoration: none;
    color: inherit;
}

.data-menu-item:hover::before {
    transform: scaleX(1);
}

.data-menu-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 12px;
    font-size: 1.1rem;
    color: white;
    transition: all 0.3s ease;
}

.data-menu-item:hover .data-menu-icon {
    transform: scale(1.1);
}

.npl-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
.dpk-bg { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
.tl-dpk3-bg { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
.tl-new-dpk-bg { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }

.data-menu-text {
    font-size: 0.85rem;
    font-weight: 600;
    color: #495057;
    margin: 0;
    line-height: 1.2;
}

@media (max-width: 768px) {
    .data-menu-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
    }
    
    .data-menu-item {
        padding: 15px 10px;
    }
    
    .data-menu-icon {
        width: 35px;
        height: 35px;
        font-size: 1rem;
    }
    
    .data-menu-text {
        font-size: 0.8rem;
    }
}

@media (max-width: 480px) {
    .data-menu-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<div class="data-menu-component">
    <h3 class="data-menu-title">
        <i class="fas fa-database"></i> Data Management
    </h3>
    
    <div class="data-menu-grid">
        <!-- Generate New NPL -->
        <a href="generate-new_npl.php" class="data-menu-item">
            <div class="data-menu-icon npl-bg">
                <i class="fas fa-chart-line"></i>
            </div>
            <p class="data-menu-text">Generate New NPL</p>
        </a>

        <!-- Generate New DPK -->
        <a href="generate-new_dpk.php" class="data-menu-item">
            <div class="data-menu-icon dpk-bg">
                <i class="fas fa-coins"></i>
            </div>
            <p class="data-menu-text">Generate New DPK</p>
        </a>

        <!-- Update TL DPK3 -->
        <a href="update-tl-dpk3.php" class="data-menu-item">
            <div class="data-menu-icon tl-dpk3-bg">
                <i class="fas fa-target"></i>
            </div>
            <p class="data-menu-text">Update TL DPK3</p>
        </a>

        <!-- Update TL New DPK -->
        <a href="update-tl-new-dpk.php" class="data-menu-item">
            <div class="data-menu-icon tl-new-dpk-bg">
                <i class="fas fa-bullseye"></i>
            </div>
            <p class="data-menu-text">Update TL New DPK</p>
        </a>
    </div>
</div>

<script>
// Add loading effect for menu component
document.querySelectorAll('.data-menu-item').forEach(item => {
    item.addEventListener('click', function(e) {
        const icon = this.querySelector('.data-menu-icon i');
        const originalClass = icon.className;
        
        // Show loading spinner
        icon.className = 'fas fa-spinner fa-spin';
        
        // Restore original icon after delay
        setTimeout(() => {
            icon.className = originalClass;
        }, 3000);
    });
});
</script>
