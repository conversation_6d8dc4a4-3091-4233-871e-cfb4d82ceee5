<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Data Management Menu</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .menu-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin: 30px auto;
            max-width: 900px;
        }

        .menu-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .menu-title {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 8px;
        }

        .menu-subtitle {
            color: #6c757d;
            font-size: 1rem;
        }

        .menu-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .menu-item {
            background: white;
            border-radius: 12px;
            padding: 25px 20px;
            text-decoration: none;
            color: inherit;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            border: 1px solid #e9ecef;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .menu-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .menu-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
            text-decoration: none;
            color: inherit;
        }

        .menu-item:hover::before {
            transform: scaleX(1);
        }

        .menu-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            font-size: 1.3rem;
            color: white;
            transition: all 0.3s ease;
        }

        .menu-item:hover .menu-icon {
            transform: scale(1.1);
        }

        .npl-icon { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .dpk-icon { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
        .tl-dpk3-icon { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
        .tl-new-dpk-icon { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }

        .menu-text {
            font-size: 0.95rem;
            font-weight: 600;
            color: #495057;
            margin: 0;
        }

        .menu-desc {
            font-size: 0.8rem;
            color: #6c757d;
            margin-top: 5px;
            line-height: 1.3;
        }

        @media (max-width: 768px) {
            .menu-container {
                margin: 20px;
                padding: 25px 20px;
            }

            .menu-title {
                font-size: 1.6rem;
            }

            .menu-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 15px;
            }

            .menu-item {
                padding: 20px 15px;
            }
        }

        @media (max-width: 480px) {
            .menu-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="menu-container">
            <!-- Header -->
            <div class="menu-header">
                <h1 class="menu-title">
                    <i class="fas fa-database"></i> Data Management
                </h1>
                <p class="menu-subtitle">
                    Pilih menu untuk mengelola data NPL, DPK, dan Target Level
                </p>
            </div>

            <!-- Menu Grid -->
            <div class="menu-grid">
                <!-- Generate New NPL -->
                <a href="generate-new_npl.php" class="menu-item">
                    <div class="menu-icon npl-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <p class="menu-text">Generate New NPL</p>
                    <p class="menu-desc">Generate dan cache data New NPL</p>
                </a>

                <!-- Generate New DPK -->
                <a href="generate-new_dpk.php" class="menu-item">
                    <div class="menu-icon dpk-icon">
                        <i class="fas fa-coins"></i>
                    </div>
                    <p class="menu-text">Generate New DPK</p>
                    <p class="menu-desc">Generate data New DPK</p>
                </a>

                <!-- Update TL DPK3 -->
                <a href="update-tl-dpk3.php" class="menu-item">
                    <div class="menu-icon tl-dpk3-icon">
                        <i class="fas fa-target"></i>
                    </div>
                    <p class="menu-text">Update TL DPK3</p>
                    <p class="menu-desc">Update Target Level DPK3</p>
                </a>

                <!-- Update TL New DPK -->
                <a href="update-tl-new-dpk.php" class="menu-item">
                    <div class="menu-icon tl-new-dpk-icon">
                        <i class="fas fa-bullseye"></i>
                    </div>
                    <p class="menu-text">Update TL New DPK</p>
                    <p class="menu-desc">Update Target Level New DPK</p>
                </a>
            </div>

            <!-- Quick Links -->
            <div class="text-center mt-4">
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="window.open('view_cache_data.php', '_blank')">
                        <i class="fas fa-eye"></i> View Cache
                    </button>
                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="window.open('debug_progress.php', '_blank')">
                        <i class="fas fa-bug"></i> Diagnostic
                    </button>
                    <button type="button" class="btn btn-outline-info btn-sm" onclick="location.reload()">
                        <i class="fas fa-sync"></i> Refresh
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Add loading effect when clicking menu items
        document.querySelectorAll('.menu-item').forEach(item => {
            item.addEventListener('click', function(e) {
                const icon = this.querySelector('.menu-icon i');
                const originalClass = icon.className;
                
                // Show loading spinner
                icon.className = 'fas fa-spinner fa-spin';
                
                // Restore original icon after delay (in case navigation fails)
                setTimeout(() => {
                    icon.className = originalClass;
                }, 3000);
            });
        });

        // Add ripple effect on click
        document.querySelectorAll('.menu-item').forEach(item => {
            item.addEventListener('click', function(e) {
                const ripple = document.createElement('span');
                const rect = this.getBoundingClientRect();
                const size = Math.max(rect.width, rect.height);
                const x = e.clientX - rect.left - size / 2;
                const y = e.clientY - rect.top - size / 2;
                
                ripple.style.cssText = `
                    position: absolute;
                    width: ${size}px;
                    height: ${size}px;
                    left: ${x}px;
                    top: ${y}px;
                    background: rgba(102, 126, 234, 0.3);
                    border-radius: 50%;
                    transform: scale(0);
                    animation: ripple 0.6s ease-out;
                    pointer-events: none;
                `;
                
                this.appendChild(ripple);
                
                setTimeout(() => {
                    ripple.remove();
                }, 600);
            });
        });

        // Add CSS for ripple animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes ripple {
                to {
                    transform: scale(2);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
