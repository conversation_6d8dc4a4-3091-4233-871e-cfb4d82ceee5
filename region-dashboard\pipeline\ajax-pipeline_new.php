<?php
session_start();
ini_set('max_execution_time', 0);
require_once '../../koneksi.php';
require_once '../../region-dashboard/region-function.php';
require_once '../../functions.php';

$kode_wilayah = $_SESSION['kode_wilayah'] ?? 'F';

if ($_GET['action'] == 'table_data' or 1) {
    $columns = array(
        0  => 'no',
        1  => 'nama_debitur',
        2  => 'norek',
        3  => 'cif',
        4  => 'main_branch',
        5  => 'branch',
        6  => 'tgl_realisasi',
        7  => 'tgl_jatuh_tempo',
        8  => 'type',
        9  => 'plafond',
        10  => 'jw',
        11 => 'baki_debet',
        12 => 'last_six',
        13 => 'nama_mantri',
        14 => 'id',
        15 => 'pn_mantri',
        16 => 'tgl_kunjungan',
        17 => 'hasil_tl'
    );

    $limit     = mysqli_real_escape_string($koneksi, $_POST['length']);
    $start     = mysqli_real_escape_string($koneksi, $_POST['start']);
    $order     = $columns[mysqli_real_escape_string($koneksi, $_POST['order']['0']['column'])];
    $dir       = mysqli_real_escape_string($koneksi, $_POST['order']['0']['dir']);
    $datedprev = mysqli_real_escape_string($koneksi, $_POST['datedprev']);
    // $datedprev = '2025-03-31';
    $tgl_max   = mysqli_real_escape_string($koneksi, $_POST['tgl_max']);
    $kategori_pipeline = $_POST['kategori_pipeline'];

    if (trim($_POST['mantri']) != '') {
        $pn_mantri = mysqli_real_escape_string($koneksi, $_POST['mantri']);
        $pn_mantri = sprintf("%08d", $pn_mantri);
    }





    if ($_POST['branch'] != '') {
        $cek_type = cek_branch2($_POST['branch']);
    } else {
        $cek_type = '';
    }


    if ($cek_type == 'un') {
        $str = "and branch = '" . $_POST['branch'] . "'";
    } else if ($cek_type == 'kc') {
        $str = "and pipeline_real_gen.main_branch = '" . $_POST['branch'] . "'";
    } else if ($cek_type == 'area') {
        $str = "and branch IN (select kode_uker from uker where id_area = '" . $_POST['branch'] . "')";
    } else {
        $str = "and branch IN (select kode_uker from uker where region = '$kode_wilayah' and uker_type = 'un')";
    }

    if ($pn_mantri != '') {
        $str .= " and pipeline_real_gen.pn_mantri = '$pn_mantri'";
    }

    if (trim($_POST['pic']) != '') {
        $pic = mysqli_real_escape_string($koneksi, $_POST['pic']);

        switch ($pic) {
            case "mantri":
                $range = " and pipeline_real_gen.plafond <= 50000000";
                break;
            case "kaunit":
                $range = " and pipeline_real_gen.plafond <= 100000000 and pipeline_real_gen.plafond > 50000000";
                break;
            case "mbm":
                $range = " and pipeline_real_gen.plafond <= 200000000 and pipeline_real_gen.plafond > 100000000";
                break;
            case "pinca":
                $range = " and pipeline_real_gen.plafond <= 1000000000 and pipeline_real_gen.plafond > 200000000";
                break;
        }



        $str .= $range;
    }


    switch ($kategori_pipeline) {
        case '1':
            $sql_where = "kategori = 'Pipeline 1'";
            break;
        case '2':
            $sql_where = "kategori = 'Pipeline 50'";
            break;
        case '3':
            $sql_where = "kategori = 'Pipeline Lupus'";
            break;
        default:
            $sql_where = "kategori <> ''";
            break;
    }


    $sql_base = "SELECT pipeline_real_gen.*, mantri.nama_mantri 
                 FROM pipeline_real_gen
                 LEFT JOIN mantri ON pipeline_real_gen.pn_mantri = mantri.pn 
                 WHERE $sql_where AND pipeline_real_gen.periode = ? $str ";

    $sql_count = "SELECT COUNT(*) as jumlah FROM pipeline_real_gen WHERE $sql_where AND periode = ? $str";

    //echo $sql_base;
    //$datedprev = '2025-03-31';

    $stmt = $koneksi->prepare($sql_count);
    $stmt->bind_param('s', $datedprev);
    $stmt->execute();
    $rs_c          = $stmt->get_result();
    $dat_c         = $rs_c->fetch_array();
    $totalData     = $dat_c['jumlah'];
    $totalFiltered = $totalData;

    $search = $_POST['search']['value'];
    if (!empty($search)) {
        $sql_data = "$sql_base AND pipeline_real_gen.nama_debitur LIKE ? ORDER BY $order $dir LIMIT ? OFFSET ?";
        $stmt     = $koneksi->prepare($sql_data);
        $like     = '%' . $search . '%';
        $stmt->bind_param('ssii', $datedprev, $like, $limit, $start);
        $stmt->execute();
        $query = $stmt->get_result();

        $sql_filtered = "$sql_count AND nama_debitur LIKE ?";
        $stmt         = $koneksi->prepare($sql_filtered);
        $stmt->bind_param('ss', $datedprev, $like);
        $stmt->execute();
        $rs_f          = $stmt->get_result();
        $dat_f         = $rs_f->fetch_array();
        $totalFiltered = $dat_f['jumlah'];
    } else {
        $sql_data = "$sql_base ORDER BY $order $dir LIMIT ? OFFSET ?";
        $stmt = $koneksi->prepare($sql_data);
        $stmt->bind_param('sii', $datedprev, $limit, $start);
        $stmt->execute();
        $query = $stmt->get_result();
    }

    $data = array();
    if (!empty($query)) {
        $no = $start + 1;
        while ($r = $query->fetch_assoc()) {
            $nestedData['no']              = $no;
            $nestedData['main_branch']     = get_nama_uker($r['main_branch'], $koneksi);
            $nestedData['branch']          = get_nama_uker($r['branch'], $koneksi);
            $nestedData['norek']           = $r['norek'];
            $nestedData['cif']             = $r['cif'];
            $nestedData['nama_debitur']    = $r['nama_debitur'];
            $nestedData['tgl_realisasi']   = tgl_periode($r['tgl_realisasi']);
            $nestedData['tgl_jatuh_tempo'] = tgl_periode($r['tgl_jatuh_tempo']);
            $nestedData['type']            = get_type($r['type']);
            $nestedData['plafond']         = number_format($r['plafond']);
            $nestedData['jw']              = number_format($r['jangka_waktu']);
            $nestedData['baki_debet']      = number_format($r['baki_debet']);

            $jumkol = 0;
            $arr_days = getLastDaysOfLastSixMonths($r['periode']);

            foreach ($arr_days as $day) {
                $sql_cek  = 'SELECT kol FROM lw321 WHERE periode = ? AND norek = ?';
                $stmt_kol = $koneksi->prepare($sql_cek);
                $stmt_kol->bind_param('ss', $day, $r['norek']);
                $stmt_kol->execute();
                $rs_cek   = $stmt_kol->get_result();
                $dat_cek  = $rs_cek->fetch_array();
                $jumkol  += $dat_cek['kol'];
            }

            $nestedData['last_six'] = $jumkol > 6
                ? '<span class="badge rounded-pill text-bg-warning">Pernah DPK</span>'
                : '<span class="badge rounded-pill text-bg-success text-white">Selalu Lancar</span>';

            $nestedData['mantri'] = $r['nama_mantri'] != '' ? $r['nama_mantri'] : $r['pn_mantri'];

            $sql_cif_real = 'SELECT norek, tgl_realisasi, plafond FROM lw321 WHERE tgl_realisasi > ? AND periode = ? AND cif = ?';
            $stmt         = $koneksi->prepare($sql_cif_real);
            $stmt->bind_param('sss', $datedprev, $tgl_max, $r['cif']);
            $stmt->execute();
            $rs_cif_real  = $stmt->get_result();
            $dat_cif_real = $rs_cif_real->fetch_array();

            if ($dat_cif_real['plafond'] == '') {
                $nestedData['aksi'] = '<a href="#" class="btn btn-warning btn-sm btn-tindak-lanjut" data-norek="' . $r['norek'] . '">Tindak Lanjut</a>';
            } else {
                $nestedData['aksi'] = 'Sudah Real. Tgl: ' . $dat_cif_real['tgl_realisasi'] . '<br>Plafond : ' . number_format($dat_cif_real['plafond']);
            }

            // Ambil data kunjungan Tindak Lanjut
            $sql_tl  = 'SELECT tgl_kunjungan, minat, keterangan FROM tl_pipeline_real WHERE norek = ?';
            $stmt_tl = $koneksi->prepare($sql_tl);
            $stmt_tl->bind_param('s', $r['norek']);
            $stmt_tl->execute();
            $rs_tl   = $stmt_tl->get_result();
            $data_tl = $rs_tl->fetch_assoc();

            if ($data_tl) {
                $nestedData['tgl_kunjungan'] = tgl_periode($data_tl['tgl_kunjungan']);
                $nestedData['hasil_tl'] = '<strong>Minat:</strong> ' . htmlspecialchars($data_tl['minat']) . '<br><strong>Keterangan:</strong> ' . nl2br(htmlspecialchars($data_tl['keterangan']));
            } else {
                $nestedData['tgl_kunjungan'] = '-';
                $nestedData['hasil_tl'] = '<em>Belum ada TL</em>';
            }


            $data[] = $nestedData;
            $no++;
        }
    }

    $json_data = array(
        'draw'            => intval($_POST['draw']),
        'recordsTotal'    => intval($totalData),
        'recordsFiltered' => intval($totalFiltered),
        'data'            => $data
    );

    echo json_encode($json_data);
}
