<?php

if ($_POST['pilih-uker'] == '') {
    $uker = get_uker_region_kc('F');
    $uker2 = get_uker_region_kc('F');
} else {
    $uker = get_list_unit_active($_POST['pilih-uker']);
    $uker2 = get_list_unit_active($_POST['pilih-uker']);
}

if ($_GET['view'] == 'unit') {
    $uker = get_list_unit_active_f();
    $uker2 = get_list_unit_active_f();
}

?>

<?php
ini_set('max_execution_time', '0');

//$arr_akhir_bln = array('2024-01-31', '2024-02-29', '2024-03-31', '2024-04-30', '2024-05-31', '2024-06-30', '2024-07-31', '2024-08-31', '2024-09-30', '2024-10-31', '2024-11-30', '2024-12-31');

$satuan = 1000000;
$month = date('m');
$year = date('Y');

//

if ($_POST['year'] == '') {
    $year = date('Y');
} else {
    $year = $_POST['year'];
}

if (isset($year)) {
    $tahun = intval($year);
    $arr_akhir_bln = [];

    for ($bulan = 1; $bulan <= 12; $bulan++) {
        $tgl = date("Y-m-t", strtotime("$tahun-$bulan-01"));
        $arr_akhir_bln[] = $tgl;
    }
}

$z = 3;
for ($i = 1; $i <= $hari; $i++) {
    $arr_i[$i] = $z;
    $z++;
}
$series_hari = implode(',', $arr_i);

?>
<link rel="stylesheet" href="https://cdn.datatables.net/buttons/1.7.0/css/buttons.dataTables.min.css">

<script src="https://cdn.datatables.net/buttons/1.7.0/js/dataTables.buttons.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
<script src="https://cdn.datatables.net/buttons/1.7.0/js/buttons.html5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/1.7.0/js/buttons.print.min.js"></script>


<!-- fungsi datatable -->

<?php include 'menu-trends.php'; ?>
<h5 class="text-center">Trend NEW NPL (Rp)</h5>
<div class="text-center">
    <a href="?menu=trend_new_dpk&view=unit"><button class="button btn-warning text-dark">Unit View</button></a>
</div>
<script>
    document.title = 'Series NEW NPL <?php echo tgl_periode($tgl_max); ?>'
</script>
<p class="text-center">
<div class="container-fluid">
    <form action="" method="post">
        <input type="hidden" name="pilih-uker" value="<?php echo $_POST['pilih-uker'] ?>">
        <label>Pilih Tahun Periode : </label>
        <select name="year" onchange="this.form.submit()">
            <?php
            if ($_POST['year'] == '') {
                $_POST['year'] = date('Y');
            }

            for ($y = ($year - 1); $y <= ($year + 1); $y++) {
                if ($y == $_POST['year']) {
                    $sl = 'selected';
                } else {
                    $sl = '';
                }
            ?>
                <option value="<?php echo $y ?>" <?php echo $sl ?>><?php echo $y; ?></option>
            <?php } ?>
        </select>
    </form>
</div>
</p>

<?php
$sql_max_real = 'select max(periode) from l1133';
$rs_max = mysqli_query($koneksi, $sql_max_real);
$tgl_max = mysqli_fetch_array($rs_max);

while ($dat_u = mysqli_fetch_array($uker)) {
    $i++;
    $jum_mantri = get_jumlah_mantri($dat_u['kode_uker']);

?>
<?php
    foreach ($arr_akhir_bln as $ab) {
        $periode_ = $ab;
        $new_dpk = new_dpk($dat_u['kode_uker'], $periode_);

        $new_dpk['deb'];
        $new_dpk['os'];
        $sql_insert = "insert into new_dpk_cache 
                                        (periode, branch_code, branch_name, region, main_branch, new_dpk_os, new_dpk_deb)
                                         values ('$periode_', '" . $dat_u['kode_uker'] . "', '" . $dat_u['nama_uker'] . "', '" . $dat_u['region'] . "', '" . $dat_u['main_branch'] . "', " . $new_dpk['os'] . ", '" . $new_dpk['deb'] . "') 
                                         ON DUPLICATE KEY UPDATE new_dpk_os = '" . $new_dpk['os'] . "', new_dpk_deb = '" . $new_dpk['deb'] . "'";
        //echo $sql_insert;
        $rs_insert = mysqli_query($koneksi, $sql_insert);
    }
} ?>