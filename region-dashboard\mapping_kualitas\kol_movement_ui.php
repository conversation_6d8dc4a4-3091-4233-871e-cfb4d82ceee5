<div class="container mt-4">
    <h5>Dashboard Pergerakan Kolektibilitas</h5>
    <form id="filterForm" class="mb-3">
        <div class="row g-2">
            <div class="col-md-4">
                <input type="month" class="form-control" name="periode_awal" required>
            </div>
            <div class="col-md-4">
                <input type="month" class="form-control" name="periode_akhir" required>
            </div>
            <div class="col-md-4">
                <button class="btn btn-primary" type="submit"><PERSON><PERSON><PERSON><PERSON></button>
            </div>
        </div>
    </form>

    <canvas id="chartRingkasan" height="100"></canvas>
    <table class="table table-bordered mt-4" id="tabelMovement">
        <thead>
            <tr>
                <th>Nomor Rekening</th>
                <th>Periode Awal</th>
                <th>Kolek Awal</th>
                <th>Periode Akhir</th>
                <th>Kolek Akhir</th>
                <th>Movement</th>
            </tr>
        </thead>
        <tbody></tbody>
    </table>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.getElementById('filterForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const pa = this.periode_awal.value;
        const pb = this.periode_akhir.value;

        fetch(`kol_movement_controller.php?periode_awal=${pa}-01&periode_akhir=${pb}-01`)
            .then(res => res.json())
            .then(data => {
                const tbody = document.querySelector('#tabelMovement tbody');
                tbody.innerHTML = '';
                data.nominatif.forEach(d => {
                    tbody.innerHTML += `
                    <tr>
                        <td>${d.Nomor_Rekening}</td>
                        <td>${d.periode_awal}</td>
                        <td>${d.kol_awal}</td>
                        <td>${d.periode_akhir}</td>
                        <td>${d.kol_akhir}</td>
                        <td>${d.movement}</td>
                    </tr>
                `;
                });

                const ctx = document.getElementById('chartRingkasan').getContext('2d');
                const labels = Object.keys(data.summary);
                const counts = Object.values(data.summary);

                if (window.kolChart) window.kolChart.destroy();

                window.kolChart = new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: labels,
                        datasets: [{
                            label: 'Jumlah Rekening',
                            data: counts,
                            backgroundColor: 'rgba(75, 192, 192, 0.7)'
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
            });
    });
</script>