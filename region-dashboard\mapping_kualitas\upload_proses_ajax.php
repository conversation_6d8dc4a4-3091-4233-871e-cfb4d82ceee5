<?php
require_once "../../koneksi.php";

ini_set('max_execution_time', 0);
header('Content-Type: text/plain'); // biar output tidak dianggap HTML oleh JS

$responseLog = "";

if ($mysqli->connect_error) {
    die("Koneksi gagal: " . $mysqli->connect_error);
}

if (isset($_FILES['csv_file']) && $_FILES['csv_file']['error'] === UPLOAD_ERR_OK) {
    $fileTmp = $_FILES['csv_file']['tmp_name'];
    $responseLog .= " File diterima: $fileTmp\n";

    $handle = fopen($fileTmp, 'r');
    if (!$handle) {
        die("❌ Gagal membuka file.");
    }

    $header = fgetcsv($handle, 10000, ';');
    $responseLog .= "📌 Header CSV: " . implode(', ', $header) . "\n";

    $success = $fail = 0;
    $rowNumber = 1;

    while (($data = fgetcsv($handle, 10000, ';')) !== false) {
        $rowNumber++;

        if (count($data) != count($header)) {
            $fail++;
            $responseLog .= "❌ Baris $rowNumber kolom tidak cocok (" . count($data) . " != " . count($header) . ")\n";
            continue;
        }

        $data = array_map('trim', $data);

        foreach ($data as &$val) {
            if (preg_match('/\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4}/', $val)) {
                $val = date('Y-m-d', strtotime(str_replace('/', '-', $val)));
            } elseif (preg_match('/^[\d.]+$/', $val)) {
                $val = str_replace('.', '', $val);
            }
        }

        $placeholders = implode(',', array_fill(0, count($data), '?'));
        $stmt = $mysqli->prepare("INSERT INTO mapping_kualitas VALUES ($placeholders)");

        if ($stmt) {
            $stmt->bind_param(str_repeat('s', count($data)), ...$data);

            if ($stmt->execute()) {
                $success++;
            } else {
                $fail++;
                $responseLog .= "❌ Baris $rowNumber GAGAL eksekusi: " . $stmt->error . "\n";
            }

            $stmt->close();
        } else {
            $fail++;
            $responseLog .= "❌ Baris $rowNumber GAGAL prepare: " . $mysqli->error . "\n";
        }
    }

    fclose($handle);
    $responseLog .= "\n🎯 Selesai: $success berhasil, $fail gagal.\n";
    echo nl2br($responseLog); // ubah newline jadi <br> biar enak dibaca di HTML
} else {
    echo "⚠️ Tidak ada file CSV dikirim atau terjadi error.";
}
