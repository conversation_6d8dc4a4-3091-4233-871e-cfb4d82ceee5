<?php
session_start();
require_once '../koneksi.php';
require_once '../functions.php';
require_once './region-function.php';

$uker = get_list_unit_active_f();

$sql_max = 'select max(periode) from lw321';
$rs_max_lw = mysqli_query($koneksi, $sql_max);
$dat_max_lw = mysqli_fetch_array($rs_max_lw);
$tgl_max = $dat_max_lw[0];

?>
<link rel="stylesheet" href="https://cdn.datatables.net/buttons/1.7.0/css/buttons.dataTables.min.css">
<script src="https://cdn.datatables.net/buttons/1.7.0/js/dataTables.buttons.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
<script src="https://cdn.datatables.net/buttons/1.7.0/js/buttons.html5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/1.7.0/js/buttons.print.min.js"></script>


<!-- fungsi datatable -->

<script>
    $(document).ready(function() {
        $('#tabel-data').DataTable({
            // script untuk membuat export data 
            "paging": false,
            "autoWidth": true,

            "rowCallback": function(row, data, index) {

                $("td:eq(8)", row).css('color', '#65B741')

                $("td:eq(9)", row).css('color', '#65B741')

                $("td:eq(6)", row).css('color', '#C40C0C')

                $("td:eq(7)", row).css('color', '#C40C0C')


            },
            "footerCallback": function(row, data, start, end, display) {
                var api = this.api();
                nb_cols = api.columns().nodes().length;
                var j = 2;

                let intVal = function(i) {
                    return typeof i === 'string' ? i.replace(/[\$,]/g, '') * 1 : typeof i === 'number' ? i : 0;
                }

                while (j < (nb_cols)) {
                    var pageTotal = api
                        .column(j, {
                            page: 'current'
                        })
                        .data()
                        .reduce(function(a, b) {
                            return intVal(Number(a)) + intVal(Number(b));

                        }, 0);

                    // Update footer


                    var numberRenderer = $.fn.dataTable.render.number(',', '.', 0).display;


                    $(api.column(j).footer()).html(numberRenderer(pageTotal));
                    j++;

                }


            },
            order: [
                [7, 'desc']
            ],

            dom: 'Bfrtip',
            buttons: [{
                    extend: 'copyHtml5',
                    footer: true
                },
                {
                    extend: 'excelHtml5',
                    footer: true
                },
                {
                    extend: 'csvHtml5',
                    footer: true
                },
                {
                    extend: 'pdfHtml5',
                    footer: true
                }
            ],

            columnDefs: [{
                    targets: [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13],
                    render: $.fn.dataTable.render.number(',', '.', 0, '')
                },
                {
                    "targets": [2, 3,
                        4,
                        5,
                        6,
                        7, 8, 9, 10, 11, 12, 13
                    ], // your case first column
                    "className": "text-end",

                }
            ]
        })
    });
</script>
<h5 class="text-center"> <b>TINDAK LANJUT DPK3</b> </h5>
<?php if ($_SESSION['role'] == 'god') { ?>
    <div class="text-center">
        <a href="?menu=dpk3unit"><button class="button btn-warning text-dark">Unit View</button></a>
    </div>
<?php } ?>
<script>
    document.title = 'Tindak Lanjut DPK3 <?php echo tgl_periode($tgl_max); ?>'
</script>
<div class="container-fluid">
    <table id="tabel-data" class="table table-striped table-bordered display" style="width:100%" data-page-length='50'>
        <thead class="table-warning">
            <tr>

                <th rowspan="2">Code</th>
                <th rowspan="2">Branch <br>Office</th>

                <th colspan="2" class="text-center">DPK3 Akhir Bulan <br>All NPDD<br>(<?php echo tgl_periode($datedprev); ?>)</th>
                <th colspan="2" class="text-center">DPK3 NPDD s.d.<br>(<?php echo tgl_periode($tgl_max); ?>)</th>

                <th colspan="2" class="text-center">Memburuk ke NPL posisi <br><?php echo tgl_periode($tgl_max); ?></th>
                <th colspan="2" class="text-center">TL Bayar / Restruk<br> All NPDD <br><?php echo tgl_periode($tgl_max); ?></th>
                <th colspan="2" class="text-center">Lunas <br><?php echo tgl_periode($tgl_max); ?></th>
                <th colspan="2" class="text-center">Sisa DPK3 <br><?php echo tgl_periode($tgl_max); ?></th>

            </tr>
            <tr>

                <th class="text-center">Deb</th>
                <th class="text-center">Rp</th>
                <th class="text-center">Deb</th>
                <th class="text-center">Rp</th>
                <th class="text-center">Deb</th>
                <th class="text-center">Rp</th>
                <th class="text-center">Deb</th>
                <th class="text-center">Rp</th>
                <th class="text-center">Deb</th>
                <th class="text-center">Rp</th>
                <th class="text-center">Deb</th>
                <th class="text-center">Rp</th>
            </tr>
        </thead>
        <tfoot class="table-info">

            <th></th>
            <th>TOTAL</th>

            <th></th>
            <th></th>
            <th></th>
            <th></th>
            <th></th>
            <th></th>
            <th></th>
            <th></th>
            <th></th>
            <th></th>
            <th></th>
            <th></th>


        </tfoot>
        <tbody>
            <?php
            ob_flush();
            flush();

            $sql_dpk3_lunas = "select norek from DPK3_APR where norek not in (select norek from lw321 where periode = '" . $tgl_max . "')";
            $rs_l = mysqli_query($koneksi, $sql_dpk3_lunas);

            echo $sql_dpk3_lunas;
            while ($dat_l = mysqli_fetch_array($rs_l)) {
                $update = "update DPK3_APR set ket = 'Lunas' where norek = '" . $dat_l['norek'] . "'";
                echo $update;
                mysqli_query($koneksi, $update);
            }

            while ($dat_u = mysqli_fetch_array($uker)) {
                $i++;
                // $jml_mantri = get_jumlah_mantri($dunit['kode_uker']);
                // $mbm = get_mbm_dash($dunit['kode_uker']);
                // $jml_deb = get_all_deb($tgl_max, $dat_u['kode_uker']);
                $dpk3_bln1 = get_dpk3($datedprev, $dat_u['kode_uker']);
                $dpk3_bln2 = get_dpk3($datedprev, $dat_u['kode_uker'], $tgl_max);

                $type = cek_branch2($dat_u['kode_uker']);

                $sql_dpk3_br = "select t1.norek from DPK3_APR t1 join lw321 t2 on t1.norek = t2.norek and t1.next_payment < t2.next_payment and t2.periode ='" . $tgl_max . "' and t2.branch = '" . $dat_u['kode_uker'] . "' and t2.kol <= '2'";
                echo $sql_dpk3_br;
                $rs_br = mysqli_query($koneksi, $sql_dpk3_br);
                while ($dat_br = mysqli_fetch_array($rs_br)) {
                    $update_1 = "update DPK3_APR set ket = 'Bayar' where norek = '" . $dat_br['norek'] . "'";
                    mysqli_query($koneksi, $update_1);
                    echo $update_1;
                }

                // $reset_bad = "update DPK3_APR set status_tl = '' where status_tl = 'Memburuk'";
                // $sql_bad = "SELECT norek from DPK3_APR t1 JOIN lw321c t2 ON t1.norek = t2.norek where t1.periode = '$datedprev' and t2.periode = '$tgl_max' and t1.kol = '2' and t2.kol >= '3' and t2.branch = '" . $dat_u['kode_uker'] . "'";

                // $sql_bad = "SELECT sum(t1.baki_debet) as os, count(t1.baki_debet) as deb from DPK3_APR t1 JOIN lw321c t2 ON t1.norek = t2.norek where t1.periode = '$datedprev' and t2.periode = '$tgl_max' and t1.kol = '2' and t2.kol >= '3' and t2.branch = '" . $dat_u['kode_uker'] . "'";

                //  $sql_dpk3_tl = "SELECT sum(t1.baki_debet) as os, count(t1.baki_debet) as deb from lw321a t1 LEFT JOIN lw321c t2 USING(norek) where TIMESTAMPDIFF(DAY, t1.next_payment, '$datedprev') > 60 and t1.periode = '$datedprev' and t1.branch = '" . $dat_u['kode_uker'] . "' and t1.kol = '2' and t2.periode = '$tgl_max' and t2.next_payment > t1.next_payment";
                //  $sql_bad = "SELECT sum(t1.baki_debet) as os, count(t1.baki_debet) as deb from lw321a t1 JOIN lw321c t2 ON t1.norek = t2.norek where t1.periode = '$datedprev' and t2.periode = '$tgl_max' and t1.kol = '2' and t2.kol >= '3' and t2.branch = '" . $dat_u['kode_uker'] . "'";

            ?>
                <tr>

                    <td><?php echo $dat_u['kode_uker'] ?></td>
                    <td><?php echo $dat_u['nama_uker'] ?></td>

                    <td><?php echo $dpk3_bln1['deb'] ?></td>
                    <td><?php echo $dpk3_bln1['os'] ?></td>
                    <td><?php echo $dpk3_bln2['deb'] ?></td>
                    <td><?php echo $dpk3_bln2['os'] ?></td>
                    <td><?php echo $dat_bad['deb'] ?></td>
                    <td><?php echo $dat_bad['os'] ?></td>
                    <td><?php echo $dat_dpk3['deb'] ?></td>
                    <td><?php echo $dat_dpk3['os'] ?></td>
                    <td><?php echo $dat_lunas['deb'] ?></td>
                    <td><?php echo $dat_lunas['os'] ?></td>

                    <td><?php echo $sisa_dpk3_deb; ?></td>
                    <td><?php echo $sisa_dpk3_os; ?></td>




                </tr>
            <?php
                flush();
                ob_flush();
            }
            ?>
        </tbody>
        <tfoot></tfoot>
    </table>
</div>
<script>
    $(document).ready(function() {
        $('#tabel-data').DataTable();
    });
</script>