<?php
// Debug file untuk memeriksa masalah dpk3-nominatif-dashboard3.php

if (!isset($_SESSION)) {
    session_start();
}

include '../config/koneksi.php';
include '../config/function.php';

// Test basic variables
echo "<h3>🔍 Debug DPK3 Nominatif Dashboard</h3>";

// Get parameters
$_options = $_GET['options'] ?? 'SML1';
$role = $_SESSION['role'] ?? '';
$_kc = $_GET['kc'] ?? '';
$_uker = $_GET['uker'] ?? '';
$pnmantri = $_GET['pnmantri'] ?? '';

// Get max periode
$sql_max = 'SELECT MAX(periode) as max_periode FROM lw321';
$rs_max_lw = mysqli_query($koneksi, $sql_max);
$dat_max_lw = mysqli_fetch_array($rs_max_lw);
$tgl_max = $dat_max_lw['max_periode'];
$datedprev = $tgl_max;

echo "<div class='alert alert-info'>";
echo "<strong>Parameters:</strong><br>";
echo "Options: " . $_options . "<br>";
echo "Role: " . $role . "<br>";
echo "KC: " . $_kc . "<br>";
echo "Uker: " . $_uker . "<br>";
echo "Mantri: " . $pnmantri . "<br>";
echo "Date Prev: " . $datedprev . "<br>";
echo "</div>";

// Test query building
function buildTestQuery($options, $role, $kc, $uker, $pnmantri, $datedprev) {
    $kat_mapping = [
        'SML1' => 'dpk1',
        'SML2' => 'dpk2', 
        'SML3' => 'dpk3',
        'NPL' => 'npl'
    ];
    
    $kat = $kat_mapping[$options] ?? '';
    $base_conditions = [];
    
    $base_conditions[] = "kat = '$kat'";
    $base_conditions[] = "periode = '$datedprev'";
    
    if (!empty($uker)) {
        $base_conditions[] = "branch = '$uker'";
        if (!empty($pnmantri)) {
            $base_conditions[] = "pn_mantri = '" . sprintf('%08d', $pnmantri) . "'";
        }
    } elseif (!empty($kc)) {
        $base_conditions[] = "main_branch = '$kc'";
        if (!empty($pnmantri)) {
            $base_conditions[] = "pn_mantri = '" . sprintf('%08d', $pnmantri) . "'";
        }
    }
    
    $sql = "SELECT * FROM dpk3_prio_okt WHERE " . implode(' AND ', $base_conditions) . " LIMIT 10";
    return $sql;
}

$test_sql = buildTestQuery($_options, $role, $_kc, $_uker, $pnmantri, $datedprev);

echo "<div class='alert alert-warning'>";
echo "<strong>Generated Query:</strong><br>";
echo "<code>" . htmlspecialchars($test_sql) . "</code>";
echo "</div>";

// Test query execution
$rs_test = mysqli_query($koneksi, $test_sql);

if ($rs_test) {
    $row_count = mysqli_num_rows($rs_test);
    echo "<div class='alert alert-success'>";
    echo "<strong>Query Result:</strong><br>";
    echo "Rows found: " . $row_count . "<br>";
    
    if ($row_count > 0) {
        echo "<strong>Sample data:</strong><br>";
        echo "<table class='table table-sm'>";
        echo "<tr><th>No Rek</th><th>Nama Debitur</th><th>Branch</th><th>Main Branch</th><th>Kat</th></tr>";
        
        $count = 0;
        while ($row = mysqli_fetch_array($rs_test) && $count < 5) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($row['norek']) . "</td>";
            echo "<td>" . htmlspecialchars($row['nama_debitur']) . "</td>";
            echo "<td>" . htmlspecialchars($row['branch']) . "</td>";
            echo "<td>" . htmlspecialchars($row['main_branch']) . "</td>";
            echo "<td>" . htmlspecialchars($row['kat']) . "</td>";
            echo "</tr>";
            $count++;
        }
        echo "</table>";
    } else {
        echo "<strong>No data found!</strong><br>";
        
        // Test if table exists and has data
        $check_table = "SELECT COUNT(*) as total FROM dpk3_prio_okt";
        $rs_check = mysqli_query($koneksi, $check_table);
        $check_result = mysqli_fetch_array($rs_check);
        echo "Total records in dpk3_prio_okt: " . $check_result['total'] . "<br>";
        
        // Check available categories
        $check_kat = "SELECT DISTINCT kat FROM dpk3_prio_okt LIMIT 10";
        $rs_kat = mysqli_query($koneksi, $check_kat);
        echo "Available categories: ";
        while ($kat_row = mysqli_fetch_array($rs_kat)) {
            echo $kat_row['kat'] . " ";
        }
        echo "<br>";
        
        // Check available periods
        $check_periode = "SELECT DISTINCT periode FROM dpk3_prio_okt ORDER BY periode DESC LIMIT 5";
        $rs_periode = mysqli_query($koneksi, $check_periode);
        echo "Available periods: ";
        while ($periode_row = mysqli_fetch_array($rs_periode)) {
            echo $periode_row['periode'] . " ";
        }
        echo "<br>";
    }
    echo "</div>";
} else {
    echo "<div class='alert alert-danger'>";
    echo "<strong>Query Error:</strong><br>";
    echo mysqli_error($koneksi);
    echo "</div>";
}

// Test different categories
echo "<h4>🧪 Test Different Categories:</h4>";
$test_categories = ['SML1', 'SML2', 'SML3', 'NPL'];

foreach ($test_categories as $cat) {
    $test_sql_cat = buildTestQuery($cat, $role, $_kc, $_uker, $pnmantri, $datedprev);
    $rs_test_cat = mysqli_query($koneksi, $test_sql_cat);
    
    if ($rs_test_cat) {
        $count_cat = mysqli_num_rows($rs_test_cat);
        echo "<span class='badge " . ($count_cat > 0 ? 'bg-success' : 'bg-danger') . "'>";
        echo $cat . ": " . $count_cat . " records</span> ";
    }
}

echo "<br><br>";
echo "<a href='dpk3-nominatif-dashboard3.php?debug=1&options=" . $_options . "' class='btn btn-primary'>Test Main File with Debug</a>";
?>

<style>
.alert { margin: 10px 0; padding: 10px; border-radius: 5px; }
.alert-info { background: #d1ecf1; border: 1px solid #bee5eb; }
.alert-warning { background: #fff3cd; border: 1px solid #ffeaa7; }
.alert-success { background: #d4edda; border: 1px solid #c3e6cb; }
.alert-danger { background: #f8d7da; border: 1px solid #f5c6cb; }
.badge { padding: 5px 10px; margin: 2px; border-radius: 3px; color: white; }
.bg-success { background: #28a745; }
.bg-danger { background: #dc3545; }
.table { border-collapse: collapse; width: 100%; }
.table th, .table td { border: 1px solid #ddd; padding: 8px; }
.table th { background: #f8f9fa; }
</style>
