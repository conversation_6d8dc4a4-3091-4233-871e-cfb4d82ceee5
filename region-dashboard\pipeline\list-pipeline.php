<?php
//print_r($_POST);
//$datedprev = '2025-02-28';

// print_r($_SESSION);
if ($branch != '') {
  $piluker = $branch;
} else {
  $piluker = $_SESSION['kodeuker'];
}


if ($branch != '') {
  $cek_type = cek_branch2($branch);
} else {
  $cek_type = '';
}

if (trim($_GET['pn_mantri'] != '')) {
  $_POST['mantri'] = mysqli_real_escape_string($koneksi, $_GET['pn_mantri']);
}
if (trim($_POST['mantri']) != '') {
  $pn_mantri = mysqli_real_escape_string($koneksi, $_POST['mantri']);
  $pn_mantri = sprintf("%08d", $pn_mantri);
}
//$kategori_pipeline = $_POST['kategori_pipeline'] ?? '';
$kategori_pipeline = mysqli_escape_string($koneksi, $_POST['kategori_pipeline']) ?? '';

//echo $kategori_pipeline;


// switch ($kategori_pipeline) {
//   case '1':
//     $sql_where = "kategori = 'Pipeline 1'";
//     break;
//   case '2':
//     $sql_where = "kategori = 'Pipeline 50'";
//     break;
//   case '3':
//     $sql_where = "kategori = 'Pipeline Lupus'";
//     break;
//   default:
//     $sql_where = "kategori <> ''";
//     break;
// }
// echo $sql_where;

$sql = "
    SELECT 
        prg.kategori,
        COUNT(*) AS total_baseline,
        COUNT(CASE 
            WHEN lw.cif IS NOT NULL THEN 1 
        END) AS total_realisasi
    FROM pipeline_real_gen prg
    LEFT JOIN (
        SELECT DISTINCT cif
        FROM lw321
        WHERE periode = '2025-06-16' 
        AND tgl_realisasi > '2025-05-31'
    ) lw ON prg.cif = lw.cif
    GROUP BY prg.kategori
";

// echo $sql; // Debug - comment out for production
$result = mysqli_query($conn, $sql);

// Ambil data ke array
$kategori = $baseline = $realisasi = [];
while ($row = mysqli_fetch_assoc($result)) {
  $kategori[] = $row['kategori'];
  $baseline[] = (int)$row['total_baseline'];
  $realisasi[] = (int)$row['total_realisasi'];
}

// print_r($kategori); // Debug - comment out for production






if ($cek_type == 'un') {
  $str = "Kode_Uker = '" . $branch . "'";
  $str1 = "branch = '" . $branch . "'";
} else if ($cek_type == 'kc') {
  $str = "Kode_Uker IN (select kode_uker from uker where main_branch = '" . $branch . "')";
  $str1 = "main_branch = '" . $branch . "'";
} else if ($cek_type == 'area') {
  $str = "Kode_Uker IN (select kode_uker from uker where id_area = '" . $branch . "' and uker_type = 'un')";
  $str1 = "branch IN (select kode_uker from uker where id_area = '" . $branch . "' and uker_type = 'un')";
} else {
  $str = "Kode_Uker IN (select kode_uker from uker where region = '$kode_wilayah' and uker_type = 'un')";
  $str1 = "branch IN (select kode_uker from uker where region = '$kode_wilayah' and uker_type = 'un')";
}



$base_path = dirname($_SERVER['SCRIPT_NAME']);
$base_url .= $base_path == '/' ? '' : $base_path;
define('BASE_URL', $base_url);
$pilih_periode = trim(htmlspecialchars($_POST['pilih-periode'], ENT_QUOTES, 'UTF=8'));
// echo $piluker;
function url($path = '')
{
  $path = ltrim($path, '/');
  return BASE_URL . ($path ? '/' . $path : '');
}
?>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-kenU1KFdBIe4zVF0s0G1M5b4hcpxyD9F7jL+jjXkk+Q2h455rYXK/7HAuoJl+0I4" crossorigin="anonymous"></script>
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-rbsA2VBKQhggwzxH7pPCaAqO46MgnOM80zW1RWuH61DGLwZJEdK2Kadq2F9CUG65" crossorigin="anonymous">

<!-- jQuery dulu -->
<script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>

<!-- Bootstrap JS -->
<!-- <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script> -->

<!-- Bootstrap CSS -->
<!-- <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/4.5.2/css/bootstrap.css">
<link rel="stylesheet" href="https://cdn.datatables.net/1.10.24/css/dataTables.bootstrap4.min.css"> -->

<script src="https://code.jquery.com/jquery-3.5.1.js"></script>
<script src="https://cdn.datatables.net/1.10.24/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.10.24/js/dataTables.bootstrap4.min.js"></script>
<script src="https://cdn.datatables.net/buttons/1.7.0/js/dataTables.buttons.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
<script src="https://cdn.datatables.net/buttons/1.7.0/js/buttons.html5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/1.7.0/js/buttons.print.min.js"></script>

<link href="https://cdn.datatables.net/fixedcolumns/4.3.0/css/fixedColumns.dataTables.min.css" rel="stylesheet">
<script src="https://cdn.datatables.net/fixedcolumns/4.3.0/js/dataTables.fixedColumns.min.js"></script>

<!-- Bootstrap 5 CSS -->
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">

<!-- Bootstrap 5 JS Bundle (dengan Popper.js) -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

<!-- Bootstrap Icons -->
<link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css" rel="stylesheet">
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<!-- Bootstrap Icons -->




<script>
  $(function() {

    $('.table').DataTable({
      "processing": true,
      "serverSide": true,
      scrollX: true,
      scrollY: '700px',
      scrollCollapse: true,
      fixedColumns: true,
      "ajax": {
        url: 'pipeline/ajax-pipeline_new.php?action=table_data',
        dataType: 'json',
        type: 'POST',
        data: function(d) {
          d.datedprev = '<?php echo $datedprev ?>';
          d.branch = '<?php echo $piluker ?>';
          d.periode = '<?php echo $pilih_periode ?>';
          d.tgl_max = '<?php echo $tgl_max; ?>';
          d.kategori_pipeline = '<?php echo $kategori_pipeline; ?>';
          d.mantri = $('#mantri').val(); // ambil nilai dari select mantri
          d.pic = $('#pic').val(); // ambil nilai dari select pic
        }
      },
      "paging": true,
      "autoWidth": true,
      order: [
        [7, 'asc']

      ],
      fixedColumns: {
        leftColumns: 2
      },

      dom: '<"top"lBf>rt<"bottom"ip><"clear">',
      buttons: [
        'copy', 'excel'
      ],
      lengthMenu: [
        [50, 100, 200],
        [50, 100, 200]
      ],
      "columns": [{
          "data": "no"
        },
        {
          "data": "nama_debitur"
        },
        {
          "data": "norek"
        },
        {
          "data": "cif"
        },
        {
          "data": "main_branch"
        },
        {
          "data": "branch"
        },
        {
          "data": "tgl_realisasi"
        },
        {
          "data": "tgl_jatuh_tempo"
        },
        {
          "data": "type"
        },
        {
          "data": "plafond"
        },
        {
          "data": "jw"
        },
        {
          "data": "baki_debet"
        },
        {
          "data": "last_six"
        },
        {
          "data": "mantri"
        },
        {
          "data": "aksi"
        },
        {
          "data": "tgl_kunjungan"
        },
        {
          "data": "hasil_tl",
          "orderable": false
        }
      ],




    });

    $('#mantri').on('change', function() {
      table.ajax.reload(); // reload data saat mantri berubah
    });

    $('#exportCsvBtn').on('click', function() {
      const table = $('.table').DataTable();
      const ajaxParams = table.ajax.params(); // ambil parameter filter dari DataTables
      const url = new URL('<?php echo BASE_URL ?>/pipeline/export_data.php', window.location.origin);
      for (const key in ajaxParams) {
        if (typeof ajaxParams[key] === 'object') {
          for (const subKey in ajaxParams[key]) {
            url.searchParams.append(`${key}[${subKey}]`, ajaxParams[key][subKey]);
          }
        } else {
          url.searchParams.append(key, ajaxParams[key]);
        }
      }
      window.location.href = url.toString(); // trigger download
    });

  });
</script>
<script>
  $('#exportCsvBtn').on('click', function() {
    const params = $('#formFilter').serialize(); // asumsi kamu pakai form filter
    window.location = 'export_data.php?' + params;
  });
</script>
<title>Data Pipeline Realisasi</title>
<style>
  .animated-link {
    font-size: 0.875rem;
    color: #0d6efd;
    display: inline-flex;
    align-items: center;
    gap: 4px;
    text-decoration: none;
    transition: all 0.2s ease;
  }

  .animated-link:hover {
    color: #0a58ca;
    transform: translateX(2px);
    text-decoration: underline;
  }

  /* Enhanced Chart Styles */
  .chart-container {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(0, 0, 0, 0.05);
  }

  .card-header.bg-gradient {
    border-radius: 1rem 1rem 0 0 !important;
  }

  .progress-bar-animated {
    animation: progress-bar-stripes 1s linear infinite;
  }

  @keyframes progress-bar-stripes {
    0% {
      background-position: 1rem 0;
    }

    100% {
      background-position: 0 0;
    }
  }

  /* Card hover effects */
  .card {
    transition: all 0.3s ease;
  }

  .card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  }

  /* Summary cards styling */
  .bg-light {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
    transition: all 0.2s ease;
  }

  .bg-light:hover {
    background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%) !important;
    transform: scale(1.02);
  }

  /* Icon animations */
  .bi {
    transition: all 0.3s ease;
  }

  .bg-light:hover .bi {
    transform: scale(1.1) rotate(5deg);
  }

  /* Chart responsive adjustments */
  @media (max-width: 768px) {
    .chart-container {
      height: 300px !important;
      padding: 10px;
    }
  }

  /* Loading animation for chart */
  .chart-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 450px;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-radius: 12px;
  }

  .spinner-border {
    width: 3rem;
    height: 3rem;
  }
</style>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

</head>

<body>
  <?php

  // kategori pipeline berdasarkan  : kolom kategori di pipeline_real_gen
  $sql_kategori = "select distinct kategori from pipeline_real_gen";
  $rs_kategori = mysqli_query($koneksi, $sql_kategori);
  //$kategori = array();
  $i = 1;
  while ($row = mysqli_fetch_array($rs_kategori)) {
    $arr_kategori[$i] = $row['kategori'];
    $i++;
  }

  if ($arr_kategori[$kategori_pipeline] == '') {
    $sql_where = "kategori <> ''";
  } else {
    $sql_where = "kategori = '$arr_kategori[$kategori_pipeline]'";
  }
  //echo $sql_where;
  //print_r($arr_kategori);
  // $arr_kategori = array();
  // $arr_kategori = array(
  //   '1' => 'Lancar Non Restruk Sudah Berjalan >= 1 Tahun',
  //   '2' => 'Lancar Non Restruk Baki Debet 50% dari plafond',
  //   '3' => 'Lancar Lainnya',

  // );

  $arr_tiering = array(
    'mantri' => 'Mantri',
    'kaunit' => 'Kaunit',
    'mbm' => 'MBM',
    'pinca' => 'Pinca',
  );

  if ($pn_mantri != '') {
    $str1 .= " and pipeline_real_gen.pn_mantri = '$pn_mantri'";
  }

  if (trim($_POST['pic']) != '') {
    $pic = mysqli_real_escape_string($koneksi, $_POST['pic']);

    switch ($pic) {
      case "mantri":
        $range = " and pipeline_real_gen.plafond <= 50000000";
        break;
      case "kaunit":
        $range = " and pipeline_real_gen.plafond <= 100000000 and pipeline_real_gen.plafond > 50000000";
        break;
      case "mbm":
        $range = " and pipeline_real_gen.plafond <= 200000000 and pipeline_real_gen.plafond > 100000000";
        break;
      case "pinca":
        $range = " and pipeline_real_gen.plafond <= 1000000000 and pipeline_real_gen.plafond > 300000000";
        break;
    }
    $str1 .= $range;
  }

  // statistic all
  $sql_base = "SELECT count(norek) as deb, SUM(plafond) as os
  FROM pipeline_real_gen
  WHERE  pipeline_real_gen.periode = '$datedprev' and $sql_where and $str1 ";

  // echo $sql_base;
  $rs_stat = mysqli_query($koneksi, $sql_base);
  $dat_stat = mysqli_fetch_array($rs_stat);


  $sql_progress = "SELECT count(distinct norek) as deb, SUM(plafond) as os from lw321 where periode = '$tgl_max' and tgl_realisasi >= '$datedprev'
                     and cif IN (select cif from pipeline_real_gen where $sql_where and $str1)";

  //$sql_progress = "SELECT count(norek) as deb, SUM(plafond) as os from pipeline_real_gen 
  //              where periode = '$datedprev' and $sql_where and $str1 and cif IN (select cif from lw321 where periode='$tgl_max' and tgl_realisasi > '$datedprev')";

  $rs_sql_progress = mysqli_query($koneksi, $sql_progress);
  $dat_sql_progress = mysqli_fetch_array($rs_sql_progress);





  ?>



  <div class="container my-4">
    <div class="card shadow rounded-4">
      <div class="card-body p-4">
        <h3 class="card-title mb-4 text-center">Pipeline Realisasi</h3>
        <form action="" method="post" class="row g-3">

          <!-- Hidden input -->
          <input type="hidden" name="pilih-uker" value="<?= $_POST['pilih-uker'] ?? '' ?>">
          <input type="hidden" name="pilih-unit" value="<?= $_POST['pilih-unit'] ?? '' ?>">

          <!-- Kategori Pipeline -->
          <div class="col-md-4">
            <label for="kategori_pipeline" class="form-label">Kategori Pipeline</label>
            <select name="kategori_pipeline" id="kategori_pipeline" class="form-select" onchange="this.form.submit()">
              <option value="">-- Semua Kategori --</option>
              <?php
              //$i = 0;
              for ($i = 1; $i <= count($arr_kategori); $i++) {
                $selected = ($i == ($_POST['kategori_pipeline'] ?? '')) ? 'selected' : '';
                echo "<option value=\"$i\" $selected>[$i].$arr_kategori[$i]</option>";
              }


              // foreach ($arr_kategori as $in => $arrk) {
              //   $i++;
              //   $selected = ($in == ($_POST['kategori_pipeline'] ?? '')) ? 'selected' : '';
              //   echo "<option value=\"$in\" $selected>$arrk</option>";
              // }
              // 
              ?>
            </select>
          </div>

          <!-- Mantri -->
          <div class="col-md-4">
            <label for="mantri" class="form-label">Mantri</label>
            <select name="mantri" id="mantri" class="form-control" onchange="this.form.submit()">
              <option value="">-- Semua Mantri --</option>
              <?php
              // Contoh pengambilan data mantri
              $kategori_filter = $_POST['kategori_pipeline'] ?? '';

              $sql_mantri = "SELECT PERNR, COMPLETENAME FROM mantri_hc WHERE $str order by COMPLETENAME asc";
              $res = mysqli_query($koneksi, $sql_mantri);
              while ($row = mysqli_fetch_assoc($res)) {
                $selected = ($row['PERNR'] == ($_POST['mantri'] ?? '')) ? 'selected' : '';
                echo "<option value=\"{$row['PERNR']}\" $selected>{$row['COMPLETENAME']}</option>";
              }

              ?>
            </select>
          </div>


          <div class="col-md-4">
            <label for="mantri" class="form-label">Kategori PIC / Tiering</label>
            <select name="pic" id="pic" class="form-select" onchange="this.form.submit()">
              <option value="">-- Semua PIC / Tiering Putusan --</option>
              <?php
              foreach ($arr_tiering as $in => $arrk) {
                $selected = ($in == ($_POST['pic'] ?? '')) ? 'selected' : '';
                echo "<option value=\"$in\" $selected>$arrk</option>";
              }
              ?>
            </select>
          </div>

        </form>
      </div>
    </div>
  </div>

  <!-- Pipeline Chart Section -->
  <div class="container mt-4 mb-5">
    <div class="card shadow rounded-4 border-0">
      <div class="card-header bg-gradient text-white text-center py-3" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
        <h4 class="mb-0 fw-bold">
          <i class="bi bi-bar-chart-fill me-2"></i>
          Analisis Pipeline vs Realisasi per Kategori
        </h4>
      </div>
      <div class="card-body p-4">
        <!-- Chart Container -->
        <div class="chart-container position-relative" style="height: 450px;">
          <!-- Loading State -->
          <div id="chartLoading" class="chart-loading">
            <div class="text-center">
              <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
              </div>
              <p class="mt-3 text-muted">Memuat data chart...</p>
            </div>
          </div>

          <!-- Error State -->
          <div id="chartError" class="chart-loading d-none">
            <div class="text-center">
              <i class="bi bi-exclamation-triangle text-warning fs-1"></i>
              <p class="mt-3 text-muted">Gagal memuat data chart</p>
              <button class="btn btn-outline-primary btn-sm" onclick="location.reload()">
                <i class="bi bi-arrow-clockwise me-1"></i>Coba Lagi
              </button>
            </div>
          </div>

          <!-- Chart Canvas -->
          <canvas id="pipelineChart" class="d-none"></canvas>
        </div>

        <!-- Chart Legend & Summary -->
        <div class="row mt-4 text-center">
          <div class="col-md-4">
            <div class="p-3 border rounded-3 bg-light">
              <i class="bi bi-clipboard-data text-primary fs-4"></i>
              <h6 class="mt-2 mb-1 text-muted">Total Kategori</h6>
              <h5 class="fw-bold text-primary"><?= count($kategori) ?></h5>
            </div>
          </div>
          <div class="col-md-4">
            <div class="p-3 border rounded-3 bg-light">
              <i class="bi bi-graph-up text-success fs-4"></i>
              <h6 class="mt-2 mb-1 text-muted">Total Baseline</h6>
              <h5 class="fw-bold text-success"><?= number_format(array_sum($baseline)) ?></h5>
            </div>
          </div>
          <div class="col-md-4">
            <div class="p-3 border rounded-3 bg-light">
              <i class="bi bi-check-circle text-info fs-4"></i>
              <h6 class="mt-2 mb-1 text-muted">Total Realisasi</h6>
              <h5 class="fw-bold text-info"><?= number_format(array_sum($realisasi)) ?></h5>
            </div>
          </div>
        </div>

        <!-- Achievement Rate -->
        <div class="row mt-3">
          <div class="col-12">
            <div class="p-3 border rounded-3 bg-light text-center">
              <h6 class="text-muted mb-2">Overall Achievement Rate</h6>
              <?php
              $total_baseline = array_sum($baseline);
              $total_realisasi = array_sum($realisasi);
              $achievement_rate = $total_baseline > 0 ? round(($total_realisasi / $total_baseline) * 100, 2) : 0;
              ?>
              <div class="progress mb-2" style="height: 20px;">
                <div class="progress-bar bg-gradient-success progress-bar-striped progress-bar-animated"
                  role="progressbar" style="width: <?= $achievement_rate ?>%;"
                  aria-valuenow="<?= $achievement_rate ?>" aria-valuemin="0" aria-valuemax="100">
                  <span class="fw-bold"><?= $achievement_rate ?>%</span>
                </div>
              </div>
              <small class="text-muted">
                <?= number_format($total_realisasi) ?> dari <?= number_format($total_baseline) ?> target baseline
              </small>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Show loading state
      const loadingElement = document.getElementById('chartLoading');
      const errorElement = document.getElementById('chartError');
      const chartCanvas = document.getElementById('pipelineChart');

      try {
        // Data dari PHP
        const kategoriData = <?= json_encode($kategori) ?>;
        const baselineData = <?= json_encode($baseline) ?>;
        const realisasiData = <?= json_encode($realisasi) ?>;

        // Validasi data
        if (!kategoriData || !baselineData || !realisasiData) {
          throw new Error('Data tidak lengkap');
        }

        if (kategoriData.length === 0) {
          throw new Error('Tidak ada data untuk ditampilkan');
        }

        // Hide loading, show chart
        setTimeout(() => {
          loadingElement.classList.add('d-none');
          chartCanvas.classList.remove('d-none');

          const ctx = chartCanvas.getContext('2d');

          // Hitung persentase untuk setiap kategori
          const persentaseData = baselineData.map((baseline, index) => {
            return baseline > 0 ? Math.round((realisasiData[index] / baseline) * 100) : 0;
          });

          const chart = new Chart(ctx, {
            type: 'bar',
            data: {
              labels: kategoriData,
              datasets: [{
                  label: 'Total Baseline',
                  data: baselineData,
                  backgroundColor: 'rgba(54, 162, 235, 0.8)',
                  borderColor: 'rgba(54, 162, 235, 1)',
                  borderWidth: 2,
                  borderRadius: 4,
                  borderSkipped: false,
                },
                {
                  label: 'Sudah Realisasi',
                  data: realisasiData,
                  backgroundColor: 'rgba(75, 192, 192, 0.8)',
                  borderColor: 'rgba(75, 192, 192, 1)',
                  borderWidth: 2,
                  borderRadius: 4,
                  borderSkipped: false,
                }
              ]
            },
            options: {
              responsive: true,
              maintainAspectRatio: false,
              interaction: {
                intersect: false,
                mode: 'index'
              },
              plugins: {
                title: {
                  display: true,
                  text: 'Perbandingan Pipeline vs Realisasi per Kategori',
                  font: {
                    size: 16,
                    weight: 'bold'
                  },
                  padding: 20
                },
                legend: {
                  display: true,
                  position: 'top',
                  labels: {
                    usePointStyle: true,
                    padding: 20,
                    font: {
                      size: 12,
                      weight: 'bold'
                    }
                  }
                },
                tooltip: {
                  backgroundColor: 'rgba(0, 0, 0, 0.8)',
                  titleColor: 'white',
                  bodyColor: 'white',
                  borderColor: 'rgba(255, 255, 255, 0.2)',
                  borderWidth: 1,
                  cornerRadius: 8,
                  displayColors: true,
                  callbacks: {
                    title: function(context) {
                      return 'Kategori: ' + context[0].label;
                    },
                    label: function(context) {
                      const datasetLabel = context.dataset.label;
                      const value = context.parsed.y;
                      const index = context.dataIndex;
                      const percentage = persentaseData[index];

                      if (datasetLabel === 'Sudah Realisasi') {
                        return `${datasetLabel}: ${value.toLocaleString()} (${percentage}%)`;
                      }
                      return `${datasetLabel}: ${value.toLocaleString()}`;
                    },
                    afterBody: function(context) {
                      const index = context[0].dataIndex;
                      const baseline = baselineData[index];
                      const realisasi = realisasiData[index];
                      const gap = baseline - realisasi;
                      return `Gap: ${gap.toLocaleString()} rekening`;
                    }
                  }
                }
              },
              scales: {
                x: {
                  grid: {
                    display: false
                  },
                  ticks: {
                    maxRotation: 45,
                    minRotation: 0,
                    font: {
                      size: 11
                    }
                  }
                },
                y: {
                  beginAtZero: true,
                  grid: {
                    color: 'rgba(0, 0, 0, 0.1)',
                    lineWidth: 1
                  },
                  ticks: {
                    callback: function(value) {
                      return value.toLocaleString();
                    },
                    font: {
                      size: 11
                    }
                  },
                  title: {
                    display: true,
                    text: 'Jumlah Rekening',
                    font: {
                      size: 12,
                      weight: 'bold'
                    }
                  }
                }
              },
              animation: {
                duration: 1500,
                easing: 'easeInOutQuart'
              }
            }
          });

          // Update chart saat window resize
          window.addEventListener('resize', function() {
            chart.resize();
          });

        }, 1000); // Delay 1 detik untuk loading effect

      } catch (error) {
        console.error('Error loading chart:', error);

        // Show error state
        loadingElement.classList.add('d-none');
        errorElement.classList.remove('d-none');

        // Optional: Show error message in console
        console.log('Chart data:', {
          kategori: <?= json_encode($kategori) ?>,
          baseline: <?= json_encode($baseline) ?>,
          realisasi: <?= json_encode($realisasi) ?>
        });
      }
    });
  </script>

  <?php
  $persen_debitur = ($dat_stat['deb'] > 0) ? round($dat_sql_progress['deb'] / $dat_stat['deb'] * 100, 2) : 0;
  $persen_plafond = ($dat_stat['os'] > 0) ? round($dat_sql_progress['os'] / $dat_stat['os'] * 100, 2) : 0;
  ?>

  <div class="container mt-5 mb-5">
    <div class="card shadow rounded-4 border-0">
      <div class="card-body p-5">
        <h3 class="card-title text-center mb-5 fw-bold">📊 Progress Eksekusi Pipeline</h3>
        <div class="row text-center g-4">

          <!-- Total Debitur -->
          <div class="col-md-3">
            <div class="p-4 border rounded-4 shadow-sm bg-light">
              <i class="bi bi-people-fill text-primary fs-3"></i>
              <h6 class="mt-2 mb-1 text-muted">Total Debitur</h6>
              <h3 class="fw-bold"><?= number_format($dat_stat['deb'], 0, ',', '.') ?></h3>
            </div>
          </div>

          <!-- Total Plafond -->
          <div class="col-md-3">
            <div class="p-4 border rounded-4 shadow-sm bg-light">
              <i class="bi bi-cash-coin text-success fs-3"></i>
              <h6 class="mt-2 mb-1 text-muted">Total Plafond</h6>
              <h3 class="fw-bold">Rp <?= number_format($dat_stat['os'], 0, ',', '.') ?></h3>
            </div>
          </div>

          <!-- Debitur Sukses -->
          <div class="col-md-3">
            <div class="p-4 border rounded-4 shadow-sm bg-light">

              <i class="bi bi-check-circle-fill text-info fs-3"></i>
              <h6 class="mt-2 mb-1 text-muted">Deb Sukses Eksekusi</h6>
              <h3 class="fw-bold"><?= number_format($dat_sql_progress['deb'], 0, ',', '.') ?></h3>
              <div class="progress mt-2" style="height: 6px;">
                <div class="progress-bar bg-info" role="progressbar"
                  style="width: <?= $persen_debitur ?>%;" aria-valuenow="<?= $persen_debitur ?>" aria-valuemin="0"
                  aria-valuemax="100"></div>
              </div>
              <small class="text-muted d-block mt-1"><?= $persen_debitur ?>%</small>
            </div>
          </div>

          <!-- Plafond Sukses -->
          <div class="col-md-3">
            <div class="p-4 border rounded-4 shadow-sm bg-light">
              <i class="bi bi-bar-chart-fill text-warning fs-3"></i>
              <h6 class="mt-2 mb-1 text-muted">Plafond Sukses Eksekusi</h6>
              <h3 class="fw-bold">Rp <?= number_format($dat_sql_progress['os'], 0, ',', '.') ?></h3>
              <div class="progress mt-2" style="height: 6px;">
                <div class="progress-bar bg-warning" role="progressbar"
                  style="width: <?= $persen_plafond ?>%;" aria-valuenow="<?= $persen_plafond ?>" aria-valuemin="0"
                  aria-valuemax="100"></div>
              </div>
              <small class="text-muted d-block mt-1"><?= $persen_plafond ?>%</small>
            </div>
          </div>
          <div class="d-flex justify-content-end mt-4">
            <div class="dropdown">
              <button class="btn btn-warning btn-sm fw-bold dropdown-toggle d-flex align-items-center" type="button" id="dropdownMenuButton" data-bs-toggle="dropdown" aria-expanded="false">
                PILIH REPORT <i class="bi bi-caret-down-fill"></i>
              </button>
              <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="dropdownMenuButton">
                <li>
                  <a class="dropdown-item" href="?mode=report-pipeline">
                    Report Kunjungan <i class="bi bi-arrow-right"></i>
                  </a>
                </li>
                <li>
                  <a class="dropdown-item" href="?mode=report-pipeline-eksekusi">
                    Report Eksekusi <i class="bi bi-arrow-right"></i>
                  </a>
                </li>
              </ul>
            </div>
          </div>



        </div>
      </div>
    </div>
  </div>

  <div class="fluid mt-5 mb-5">

    <hr>
    <div class="row">
      <div class="col-12">
        <div class="table-responsive">
          <button id="exportCsvBtn" class="btn btn-success btn-sm">📥 Export All to CSV</button>

          <table class="table table-hover table-bordered stripped align-middle">
            <thead class="table-info text-center">
              <tr>
                <th scope="col">No</th>
                <th scope="col">Nama Debitur</th>
                <th scope="col">Norek</th>
                <th scope="col">CIF</th>
                <th scope="col">Branch Office</th>
                <th scope="col">Unit</th>
                <th scope="col">Tgl Realisasi</th>
                <th scope="col">Tgl Jatuh Tempo</th>
                <th scope="col">Loan Type</th>
                <th scope="col">Plafond</th>
                <th scope="col">JW</th>
                <th scope="col">Baki Debet</th>
                <th scope="col">6 Bln Terakhir</th>
                <th scope="col">Mantri</th>
                <th scope="col">Status TL</th>
                <th scope="col">Tgl Kunjungan</th>
                <th scope="col">Hasil TL</th>
              </tr>
            </thead>
            <tbody>
              <!-- Data populated by DataTables -->
            </tbody>
          </table>
        </div>
      </div>
    </div>
    <hr>
  </div>
  <!-- Modal Tindak Lanjut -->
  <div class="modal fade" id="modalTindakLanjut" tabindex="-1" role="dialog" aria-labelledby="modalTindakLanjutLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
      <form id="formTindakLanjut">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">Tindak Lanjut Debitur</h5>
            <button type="button" class="close btn-tutup-manual" aria-label="Tutup">
              <span aria-hidden="true">×</span>
            </button>
          </div>
          <div class="modal-body">
            <!-- Input norek tersembunyi -->
            <input type="hidden" name="norek" id="norek_hidden">

            <div class="form-group">
              <label for="tgl_kunjungan">Tanggal Kunjungan</label>
              <input type="date" class="form-control" id="tgl_kunjungan" name="tgl_kunjungan" required>
            </div>

            <div class="form-group">
              <label for="minat">Berminat</label>
              <select class="form-control" id="minat" name="minat" required>
                <option value="">-- Pilih --</option>
                <option value="Ya">Ya</option>
                <option value="Tidak">Tidak</option>
              </select>
            </div>

            <div class="form-group">
              <label for="keterangan">Keterangan</label>
              <textarea class="form-control" id="keterangan" name="keterangan" rows="3"></textarea>
            </div>
          </div>
          <div class="modal-footer">
            <button type="submit" class="btn btn-primary">Simpan</button>
            <button type="button" class="btn btn-secondary" id="btnBatalManual">Tutup</button>
          </div>
        </div>
      </form>
    </div>
  </div>

  <script>
    $(document).ready(function() {
      $('.table').on('click', '.btn-tindak-lanjut', function(e) {
        e.preventDefault();
        var norek = $(this).data('norek');

        $('#norek_hidden').val(norek);
        $('#formTindakLanjut')[0].reset(); // Reset form dulu

        // Ambil data dari database jika sudah pernah disimpan
        $.ajax({
          url: 'pipeline/get_tindak_lanjut.php',
          type: 'GET',
          data: {
            norek: norek
          },
          dataType: 'json',
          success: function(data) {
            if (data) {
              $('#tgl_kunjungan').val(data.tgl_kunjungan);
              $('#minat').val(data.minat);
              $('#keterangan').val(data.keterangan);
            }
            $('#modalTindakLanjut').modal('show');
          },
          error: function() {
            alert('Gagal mengambil data tindak lanjut.');
          }
        });
      });

      $('#formTindakLanjut').on('submit', function(e) {
        e.preventDefault();
        var formData = $(this).serialize();

        $.ajax({
          url: 'pipeline/simpan_tindak_lanjut.php',
          type: 'POST',
          data: formData,
          success: function(response) {
            $('#modalTindakLanjut').modal('hide');
            $('.table').DataTable().ajax.reload(null, false);
            alert('Data tindak lanjut berhasil disimpan.');
          },
          error: function(xhr) {
            alert('Terjadi kesalahan saat menyimpan data.\n' + xhr.responseText);
          }
        });
      });

      $('#btnBatalManual, .btn-tutup-manual').on('click', function() {
        $('#modalTindakLanjut').modal('hide');
      });
    });
  </script>