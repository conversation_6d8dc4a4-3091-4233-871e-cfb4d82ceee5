<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Data Management Navigation</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }

        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .navigation-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            margin: 40px auto;
            max-width: 1200px;
        }

        .page-header {
            text-align: center;
            margin-bottom: 50px;
        }

        .page-title {
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .page-subtitle {
            color: #6c757d;
            font-size: 1.1rem;
            font-weight: 400;
        }

        .menu-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }

        .menu-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            text-decoration: none;
            color: inherit;
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: 2px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .menu-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--primary-gradient);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .menu-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
            text-decoration: none;
            color: inherit;
        }

        .menu-card:hover::before {
            transform: scaleX(1);
        }

        .menu-card.generate-npl::before {
            background: var(--primary-gradient);
        }

        .menu-card.generate-dpk::before {
            background: var(--secondary-gradient);
        }

        .menu-card.update-tl-dpk3::before {
            background: var(--success-gradient);
        }

        .menu-card.update-tl-new-dpk::before {
            background: var(--warning-gradient);
        }

        .menu-icon {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 1.8rem;
            color: white;
            transition: all 0.3s ease;
        }

        .generate-npl .menu-icon {
            background: var(--primary-gradient);
        }

        .generate-dpk .menu-icon {
            background: var(--secondary-gradient);
        }

        .update-tl-dpk3 .menu-icon {
            background: var(--success-gradient);
        }

        .update-tl-new-dpk .menu-icon {
            background: var(--warning-gradient);
        }

        .menu-card:hover .menu-icon {
            transform: scale(1.1) rotate(5deg);
        }

        .menu-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 10px;
            text-align: center;
        }

        .menu-description {
            color: #6c757d;
            font-size: 0.95rem;
            text-align: center;
            line-height: 1.5;
            margin-bottom: 20px;
        }

        .menu-status {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            font-size: 0.85rem;
            padding: 8px 16px;
            border-radius: 20px;
            background: #f8f9fa;
            color: #495057;
            margin-top: auto;
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #28a745;
        }

        .breadcrumb-nav {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 15px 25px;
            margin-bottom: 30px;
            border: none;
        }

        .breadcrumb-item a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }

        .breadcrumb-item a:hover {
            color: #764ba2;
        }

        .breadcrumb-item.active {
            color: #495057;
            font-weight: 600;
        }

        @media (max-width: 768px) {
            .navigation-container {
                margin: 20px;
                padding: 30px 20px;
            }

            .page-title {
                font-size: 2rem;
            }

            .menu-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .menu-card {
                padding: 25px 20px;
            }
        }

        .quick-actions {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            margin-top: 40px;
            text-align: center;
        }

        .quick-actions h6 {
            color: #495057;
            margin-bottom: 15px;
            font-weight: 600;
        }

        .btn-quick {
            background: rgba(102, 126, 234, 0.1);
            border: 1px solid rgba(102, 126, 234, 0.3);
            color: #667eea;
            border-radius: 10px;
            padding: 8px 16px;
            margin: 5px;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .btn-quick:hover {
            background: #667eea;
            color: white;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- Breadcrumb Navigation -->
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb breadcrumb-nav">
                <li class="breadcrumb-item"><a href="../index.php"><i class="fas fa-home"></i> Dashboard</a></li>
                <li class="breadcrumb-item"><a href="#">Mapping Kualitas</a></li>
                <li class="breadcrumb-item active" aria-current="page">Data Management</li>
            </ol>
        </nav>

        <!-- Main Navigation Container -->
        <div class="navigation-container">
            <!-- Page Header -->
            <div class="page-header">
                <h1 class="page-title">
                    <i class="fas fa-database"></i> Data Management Center
                </h1>
                <p class="page-subtitle">
                    Kelola dan generate data NPL, DPK, dan update Target Level dengan mudah
                </p>
            </div>

            <!-- Menu Grid -->
            <div class="menu-grid">
                <!-- Generate New NPL -->
                <a href="generate-new_npl.php" class="menu-card generate-npl">
                    <div class="menu-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h3 class="menu-title">Generate New NPL</h3>
                    <p class="menu-description">
                        Generate dan cache data New NPL dengan progress tracking real-time. 
                        Sistem otomatis untuk performa optimal.
                    </p>
                    <div class="menu-status">
                        <span class="status-indicator"></span>
                        <span>Ready to Generate</span>
                    </div>
                </a>

                <!-- Generate New DPK -->
                <a href="generate-new_dpk.php" class="menu-card generate-dpk">
                    <div class="menu-icon">
                        <i class="fas fa-coins"></i>
                    </div>
                    <h3 class="menu-title">Generate New DPK</h3>
                    <p class="menu-description">
                        Generate data New DPK (Dana Pihak Ketiga) dengan sistem cache 
                        untuk analisis dan reporting yang cepat.
                    </p>
                    <div class="menu-status">
                        <span class="status-indicator"></span>
                        <span>Ready to Generate</span>
                    </div>
                </a>

                <!-- Update TL DPK3 -->
                <a href="update-tl-dpk3.php" class="menu-card update-tl-dpk3">
                    <div class="menu-icon">
                        <i class="fas fa-target"></i>
                    </div>
                    <h3 class="menu-title">Update TL DPK3</h3>
                    <p class="menu-description">
                        Update Target Level DPK3 dengan interface modern. 
                        Kelola target dan monitoring pencapaian secara real-time.
                    </p>
                    <div class="menu-status">
                        <span class="status-indicator"></span>
                        <span>Ready to Update</span>
                    </div>
                </a>

                <!-- Update TL New DPK -->
                <a href="update-tl-new-dpk.php" class="menu-card update-tl-new-dpk">
                    <div class="menu-icon">
                        <i class="fas fa-bullseye"></i>
                    </div>
                    <h3 class="menu-title">Update TL New DPK</h3>
                    <p class="menu-description">
                        Update Target Level New DPK dengan fitur batch update 
                        dan validasi otomatis untuk akurasi data.
                    </p>
                    <div class="menu-status">
                        <span class="status-indicator"></span>
                        <span>Ready to Update</span>
                    </div>
                </a>
            </div>

            <!-- Quick Actions -->
            <div class="quick-actions">
                <h6><i class="fas fa-bolt"></i> Quick Actions</h6>
                <button class="btn btn-quick" onclick="window.open('view_cache_data.php', '_blank')">
                    <i class="fas fa-eye"></i> View Cache Data
                </button>
                <button class="btn btn-quick" onclick="window.open('debug_progress.php', '_blank')">
                    <i class="fas fa-bug"></i> System Diagnostic
                </button>
                <button class="btn btn-quick" onclick="refreshPage()">
                    <i class="fas fa-sync"></i> Refresh Status
                </button>
                <button class="btn btn-quick" onclick="showHelp()">
                    <i class="fas fa-question-circle"></i> Help & Guide
                </button>
            </div>
        </div>
    </div>

    <!-- Help Modal -->
    <div class="modal fade" id="helpModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-info-circle"></i> Help & Guide</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-chart-line text-primary"></i> Generate New NPL</h6>
                            <p class="small text-muted">
                                Digunakan untuk generate data New NPL dengan sistem cache. 
                                Proses ini akan memakan waktu tergantung jumlah data.
                            </p>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-coins text-danger"></i> Generate New DPK</h6>
                            <p class="small text-muted">
                                Generate data New DPK untuk analisis dana pihak ketiga. 
                                Hasil akan disimpan dalam cache untuk akses cepat.
                            </p>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-target text-info"></i> Update TL DPK3</h6>
                            <p class="small text-muted">
                                Update target level DPK3 per branch. 
                                Pastikan data yang diinput sudah sesuai dengan ketentuan.
                            </p>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-bullseye text-success"></i> Update TL New DPK</h6>
                            <p class="small text-muted">
                                Update target level New DPK dengan validasi otomatis. 
                                Mendukung batch update untuk efisiensi.
                            </p>
                        </div>
                    </div>
                    <hr>
                    <div class="alert alert-info">
                        <h6><i class="fas fa-lightbulb"></i> Tips:</h6>
                        <ul class="mb-0">
                            <li>Selalu backup data sebelum melakukan update</li>
                            <li>Gunakan system diagnostic jika mengalami masalah</li>
                            <li>Monitor progress untuk operasi yang memakan waktu lama</li>
                            <li>Check cache data secara berkala untuk memastikan akurasi</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function refreshPage() {
            location.reload();
        }

        function showHelp() {
            const helpModal = new bootstrap.Modal(document.getElementById('helpModal'));
            helpModal.show();
        }

        // Add loading animation when clicking menu items
        document.querySelectorAll('.menu-card').forEach(card => {
            card.addEventListener('click', function(e) {
                // Add loading state
                const icon = this.querySelector('.menu-icon i');
                const originalClass = icon.className;
                icon.className = 'fas fa-spinner fa-spin';
                
                // Restore original icon after a short delay if navigation fails
                setTimeout(() => {
                    icon.className = originalClass;
                }, 3000);
            });
        });

        // Add hover sound effect (optional)
        document.querySelectorAll('.menu-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                // Optional: Add subtle sound effect
                // new Audio('hover-sound.mp3').play().catch(() => {});
            });
        });

        // Check system status on page load
        document.addEventListener('DOMContentLoaded', function() {
            // Optional: Add real-time status checking
            checkSystemStatus();
        });

        function checkSystemStatus() {
            // This could check if services are running, cache is available, etc.
            // For now, we'll just simulate it
            const statusIndicators = document.querySelectorAll('.status-indicator');
            statusIndicators.forEach(indicator => {
                // Simulate status check
                indicator.style.background = '#28a745'; // Green for ready
            });
        }
    </script>
</body>
</html>
