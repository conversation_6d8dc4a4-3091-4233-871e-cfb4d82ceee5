<?php

/**
 * API Endpoint untuk Generate NEW NPL Cache dengan Progress Streaming
 * Menangani request AJAX dan mengirim progress secara real-time
 */

require_once '../koneksi.php';
require_once '../region-function.php';

// Set headers untuk streaming response
header('Content-Type: application/json');
header('Cache-Control: no-cache');
header('X-Accel-Buffering: no'); // Disable nginx buffering

// Set time limit dan memory untuk operasi berat
set_time_limit(0);
ini_set('memory_limit', '1G');

// Disable output buffering untuk real-time streaming
if (ob_get_level()) {
    ob_end_clean();
}

// Function untuk mengirim response JSON dengan flush
function sendResponse($data)
{
    echo json_encode($data) . "\n";
    flush();
    if (function_exists('fastcgi_finish_request')) {
        fastcgi_finish_request();
    }
}

// Function untuk membuat tabel cache jika belum ada
function ensureCacheTable($koneksi)
{
    $sql = "CREATE TABLE IF NOT EXISTS new_npl_cache (
        id INT AUTO_INCREMENT PRIMARY KEY,
        periode DATE NOT NULL,
        branch_code VARCHAR(10) NOT NULL,
        branch_name VARCHAR(255),
        region VARCHAR(10),
        main_branch VARCHAR(10),
        new_npl_os DECIMAL(15,2) DEFAULT 0,
        new_npl_deb INT DEFAULT 0,
        generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY unique_periode_branch (periode, branch_code),
        INDEX idx_periode (periode),
        INDEX idx_branch (branch_code),
        INDEX idx_region (region)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

    return mysqli_query($koneksi, $sql);
}

// Main logic
$action = $_POST['action'] ?? '';

switch ($action) {
    case 'generate':
        handleGeneration();
        break;
    case 'cache_status':
        handleCacheStatus();
        break;
    case 'clear_cache':
        handleClearCache();
        break;
    default:
        sendResponse(['error' => 'Invalid action']);
        break;
}

function handleGeneration()
{
    global $koneksi;

    $year = $_POST['year'] ?? date('Y');
    $uker_filter = $_POST['uker'] ?? '';

    sendResponse([
        'type' => 'message',
        'message' => 'Starting NEW NPL cache generation...',
        'level' => 'info'
    ]);

    // Ensure cache table exists
    if (!ensureCacheTable($koneksi)) {
        sendResponse([
            'type' => 'message',
            'message' => 'Error creating cache table: ' . mysqli_error($koneksi),
            'level' => 'error'
        ]);
        return;
    }

    sendResponse([
        'type' => 'message',
        'message' => 'Cache table ready',
        'level' => 'success'
    ]);

    // Generate periods for the year
    $periods = [];
    for ($month = 1; $month <= 12; $month++) {
        $periode = date("Y-m-t", strtotime("$year-$month-01"));
        $periods[] = $periode;
    }

    // Get branch list based on filter
    if ($uker_filter == '') {
        $sql_branches = "SELECT kode_uker, nama_uker, region, main_branch 
                        FROM uker 
                        WHERE uker_type = 'un' 
                        ORDER BY region, kode_uker";
    } else {
        $sql_branches = "SELECT kode_uker, nama_uker, region, main_branch 
                        FROM uker 
                        WHERE uker_type = 'un' 
                        AND (region = '$uker_filter' OR main_branch = '$uker_filter')
                        ORDER BY region, kode_uker";
    }

    $branches_result = mysqli_query($koneksi, $sql_branches);
    $branches = [];

    while ($row = mysqli_fetch_assoc($branches_result)) {
        $branches[] = $row;
    }

    $total_branches = count($branches);
    $total_periods = count($periods);
    $total_operations = $total_branches * $total_periods;

    // Send initialization data
    sendResponse([
        'type' => 'init',
        'branches' => $total_branches,
        'periods' => $total_periods,
        'total' => $total_operations
    ]);

    sendResponse([
        'type' => 'message',
        'message' => "Processing $total_branches branches × $total_periods periods = $total_operations operations",
        'level' => 'info'
    ]);

    $current_operation = 0;
    $success_count = 0;
    $error_count = 0;

    // Process each period
    foreach ($periods as $periode) {
        sendResponse([
            'type' => 'message',
            'message' => "Processing periode: $periode",
            'level' => 'info'
        ]);

        // Process each branch for this period
        foreach ($branches as $branch_data) {
            $current_operation++;
            $branch_code = $branch_data['kode_uker'];

            try {
                // Generate NPL data using existing function
                $new_npl = new_npl($branch_code, $periode);

                $os = isset($new_npl['os']) ? $new_npl['os'] : 0;
                $deb = isset($new_npl['deb']) ? $new_npl['deb'] : 0;

                // Prepare data for insertion
                $branch_code_escaped = mysqli_real_escape_string($koneksi, $branch_code);
                $branch_name_escaped = mysqli_real_escape_string($koneksi, $branch_data['nama_uker']);
                $region_escaped = mysqli_real_escape_string($koneksi, $branch_data['region']);
                $main_branch_escaped = mysqli_real_escape_string($koneksi, $branch_data['main_branch']);

                // Insert into cache
                $sql_insert = "INSERT INTO new_npl_cache 
                              (periode, branch_code, branch_name, region, main_branch, new_npl_os, new_npl_deb)
                              VALUES 
                              ('$periode', '$branch_code_escaped', '$branch_name_escaped', '$region_escaped', '$main_branch_escaped', $os, $deb)
                              ON DUPLICATE KEY UPDATE
                              branch_name = VALUES(branch_name),
                              region = VALUES(region),
                              main_branch = VALUES(main_branch),
                              new_npl_os = VALUES(new_npl_os),
                              new_npl_deb = VALUES(new_npl_deb),
                              updated_at = CURRENT_TIMESTAMP";

                if (mysqli_query($koneksi, $sql_insert)) {
                    $success_count++;

                    // Send branch completion data
                    sendResponse([
                        'type' => 'branch_complete',
                        'branch' => $branch_code,
                        'os' => number_format($os),
                        'deb' => $deb
                    ]);
                } else {
                    $error_count++;

                    sendResponse([
                        'type' => 'branch_error',
                        'branch' => $branch_code,
                        'error' => mysqli_error($koneksi)
                    ]);
                }
            } catch (Exception $e) {
                $error_count++;

                sendResponse([
                    'type' => 'branch_error',
                    'branch' => $branch_code,
                    'error' => $e->getMessage()
                ]);
            }

            // Send progress update every 5 operations or at the end
            if ($current_operation % 5 == 0 || $current_operation == $total_operations) {
                sendResponse([
                    'type' => 'progress',
                    'current' => $current_operation,
                    'total' => $total_operations,
                    'success' => $success_count,
                    'errors' => $error_count,
                    'current_branch' => $branch_code
                ]);
            }

            // Small delay to prevent overwhelming the browser
            usleep(100000); // 0.1 second
        }

        // Send period completion
        sendResponse([
            'type' => 'period_complete',
            'period' => $periode
        ]);
    }

    // Send final completion message
    sendResponse([
        'type' => 'message',
        'message' => '=== GENERATION COMPLETED ===',
        'level' => 'success'
    ]);

    sendResponse([
        'type' => 'message',
        'message' => "Total processed: $current_operation",
        'level' => 'info'
    ]);

    sendResponse([
        'type' => 'message',
        'message' => "Successful: $success_count",
        'level' => 'success'
    ]);

    sendResponse([
        'type' => 'message',
        'message' => "Errors: $error_count",
        'level' => $error_count > 0 ? 'error' : 'info'
    ]);

    // Send final progress
    sendResponse([
        'type' => 'progress',
        'current' => $total_operations,
        'total' => $total_operations,
        'success' => $success_count,
        'errors' => $error_count,
        'current_branch' => 'Completed'
    ]);

    // Show cache statistics
    $stats_sql = "SELECT 
        COUNT(*) as total_records,
        COUNT(DISTINCT periode) as total_periods,
        COUNT(DISTINCT branch_code) as total_branches,
        SUM(new_npl_os) as total_new_npl_os,
        SUM(new_npl_deb) as total_new_npl_deb
        FROM new_npl_cache";

    $stats_result = mysqli_query($koneksi, $stats_sql);
    $stats = mysqli_fetch_assoc($stats_result);

    sendResponse([
        'type' => 'message',
        'message' => "Cache Statistics:",
        'level' => 'info'
    ]);

    sendResponse([
        'type' => 'message',
        'message' => "- Total records: " . number_format($stats['total_records']),
        'level' => 'info'
    ]);

    sendResponse([
        'type' => 'message',
        'message' => "- Total periods: " . $stats['total_periods'],
        'level' => 'info'
    ]);

    sendResponse([
        'type' => 'message',
        'message' => "- Total branches: " . $stats['total_branches'],
        'level' => 'info'
    ]);

    sendResponse([
        'type' => 'message',
        'message' => "- Total New NPL OS: " . number_format($stats['total_new_npl_os']),
        'level' => 'info'
    ]);

    sendResponse([
        'type' => 'message',
        'message' => "- Total New NPL DEB: " . number_format($stats['total_new_npl_deb']),
        'level' => 'info'
    ]);
}

function handleCacheStatus()
{
    global $koneksi;

    try {
        // Ensure cache table exists
        ensureCacheTable($koneksi);

        $stats_sql = "SELECT
            COUNT(*) as total_records,
            COUNT(DISTINCT periode) as total_periods,
            COUNT(DISTINCT branch_code) as total_branches,
            MIN(periode) as earliest_periode,
            MAX(periode) as latest_periode,
            MAX(updated_at) as last_update
            FROM new_npl_cache";

        $stats_result = mysqli_query($koneksi, $stats_sql);

        if ($stats_result) {
            $stats = mysqli_fetch_assoc($stats_result);

            // Format last update
            if ($stats['last_update']) {
                $stats['last_update'] = date('d/m/Y H:i', strtotime($stats['last_update']));
            } else {
                $stats['last_update'] = 'Never';
            }

            sendResponse([
                'success' => true,
                'stats' => $stats
            ]);
        } else {
            sendResponse([
                'success' => false,
                'message' => 'Error querying cache statistics: ' . mysqli_error($koneksi)
            ]);
        }
    } catch (Exception $e) {
        sendResponse([
            'success' => false,
            'message' => 'Error getting cache status: ' . $e->getMessage()
        ]);
    }
}

function handleClearCache()
{
    global $koneksi;

    try {
        // Clear all cache data
        $clear_sql = "DELETE FROM new_npl_cache";

        if (mysqli_query($koneksi, $clear_sql)) {
            $deleted_count = mysqli_affected_rows($koneksi);

            sendResponse([
                'success' => true,
                'message' => "Cache cleared successfully. $deleted_count records deleted.",
                'deleted_count' => $deleted_count
            ]);
        } else {
            sendResponse([
                'success' => false,
                'message' => 'Error clearing cache: ' . mysqli_error($koneksi)
            ]);
        }
    } catch (Exception $e) {
        sendResponse([
            'success' => false,
            'message' => 'Error clearing cache: ' . $e->getMessage()
        ]);
    }
}

mysqli_close($koneksi);
