<?php
    require_once '../../koneksi.php';
    require_once 'region-function.php';

    function generateNewNPL($periode, $branch_code)
    {
        global $koneksi;

        // Query untuk menghitung new_npl
        $sql = "SELECT COUNT(*) as deb, SUM(baki_debet) as os
                FROM lw321 T1 JOIN lw321 T2 ON T1.norek = T2.norek
                WHERE T1.periode = '$periode' AND T2.periode = '$periode
                AND branch = '$branch_code'
                AND kol >= '3'";

        $result = mysqli_query($koneksi, $sql);

        if ($result) {
            $row = mysqli_fetch_assoc($result);
            return [
                'deb' => (int)$row['deb'],
                'os' => (float)$row['os']
            ];
        } else {
            return false;
        }
    }

?>