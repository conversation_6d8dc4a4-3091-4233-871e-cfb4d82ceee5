<?php

if ($_POST['pilih-uker'] == '') {
    $uker = get_uker_region_kc('F');
    $uker2 = get_uker_region_kc('F');
} else {
    $uker = get_list_unit_active($_POST['pilih-uker']);
    $uker2 = get_list_unit_active($_POST['pilih-uker']);
}

if ($_GET['view'] == 'unit') {
    $uker = get_list_unit_active_f();
    $uker2 = get_list_unit_active_f();
}

?>

<?php


//$arr_akhir_bln = array('2024-01-31', '2024-02-29', '2024-03-31', '2024-04-30', '2024-05-31', '2024-06-30', '2024-07-31', '2024-08-31', '2024-09-30', '2024-10-31', '2024-11-30', '2024-12-31');

$satuan = 1000000;
$month = date('m');
$year = date('Y');

//

if ($_POST['year'] == '') {
    $year = date('Y');
} else {
    $year = $_POST['year'];
}

if (isset($year)) {
    $tahun = intval($year);
    $arr_akhir_bln = [];

    for ($bulan = 1; $bulan <= 12; $bulan++) {
        $tgl = date("Y-m-t", strtotime("$tahun-$bulan-01"));
        $arr_akhir_bln[] = $tgl;
    }
}

$z = 3;
for ($i = 1; $i <= $hari; $i++) {
    $arr_i[$i] = $z;
    $z++;
}
$series_hari = implode(',', $arr_i);

?>
<link rel="stylesheet" href="https://cdn.datatables.net/buttons/1.7.0/css/buttons.dataTables.min.css">

<script src="https://cdn.datatables.net/buttons/1.7.0/js/dataTables.buttons.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
<script src="https://cdn.datatables.net/buttons/1.7.0/js/buttons.html5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/1.7.0/js/buttons.print.min.js"></script>


<!-- fungsi datatable -->

<?php include 'menu-trends.php'; ?>

<!-- Modern Progress Interface -->
<style>
    .progress-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 20px;
        padding: 30px;
        margin: 20px 0;
        color: white;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    }

    .progress-header {
        text-align: center;
        margin-bottom: 30px;
    }

    .progress-bar-container {
        background: rgba(255, 255, 255, 0.2);
        border-radius: 25px;
        padding: 8px;
        margin: 20px 0;
    }

    .progress-bar {
        background: linear-gradient(90deg, #00c851, #007e33);
        height: 30px;
        border-radius: 20px;
        transition: width 0.5s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        box-shadow: 0 4px 15px rgba(0, 200, 81, 0.3);
    }

    .status-container {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin: 20px 0;
    }

    .status-card {
        background: rgba(255, 255, 255, 0.15);
        border-radius: 15px;
        padding: 20px;
        text-align: center;
        backdrop-filter: blur(10px);
    }

    .status-number {
        font-size: 2em;
        font-weight: bold;
        margin-bottom: 5px;
    }

    .console-output {
        background: #1a1a1a;
        border-radius: 10px;
        padding: 20px;
        margin: 20px 0;
        max-height: 300px;
        overflow-y: auto;
        font-family: 'Courier New', monospace;
        color: #00ff00;
        border: 2px solid #333;
    }

    .form-container {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin: 20px 0;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .btn-generate {
        background: linear-gradient(135deg, #ff6b6b, #ee5a24);
        border: none;
        color: white;
        padding: 15px 30px;
        border-radius: 25px;
        font-size: 16px;
        font-weight: bold;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 5px 15px rgba(238, 90, 36, 0.3);
    }

    .btn-generate:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(238, 90, 36, 0.4);
    }

    .btn-generate:disabled {
        background: #ccc;
        cursor: not-allowed;
        transform: none;
    }

    .hidden {
        display: none;
    }

    .success-message {
        background: linear-gradient(135deg, #00c851, #007e33);
        color: white;
        padding: 20px;
        border-radius: 15px;
        text-align: center;
        margin: 20px 0;
        font-size: 18px;
        font-weight: bold;
    }

    .error-message {
        background: linear-gradient(135deg, #ff4444, #cc0000);
        color: white;
        padding: 20px;
        border-radius: 15px;
        text-align: center;
        margin: 20px 0;
        font-size: 18px;
        font-weight: bold;
    }
</style>

<div class="container-fluid">
    <h2 class="text-center mb-4">
        <i class="fas fa-chart-line"></i> Generate NEW NPL Cache
    </h2>

    <!-- Form Configuration -->
    <div class="form-container">
        <form id="configForm">
            <div class="row">
                <div class="col-md-6">
                    <label class="form-label"><strong>Pilih Tahun Periode:</strong></label>
                    <select name="year" id="yearSelect" class="form-select">
                        <?php
                        $current_year = date('Y');
                        if ($_POST['year'] == '') {
                            $_POST['year'] = $current_year;
                        }

                        for ($y = ($current_year - 2); $y <= ($current_year + 1); $y++) {
                            $selected = ($y == $_POST['year']) ? 'selected' : '';
                            echo "<option value='$y' $selected>$y</option>";
                        }
                        ?>
                    </select>
                </div>
                <div class="col-md-6">
                    <label class="form-label"><strong>Pilih Unit Kerja:</strong></label>
                    <select name="pilih-uker" id="ukerSelect" class="form-select">
                        <option value="">Semua Unit Kerja</option>
                        <?php
                        // Get list of regions/main branches for selection
                        $sql_regions = "SELECT DISTINCT region FROM uker WHERE uker_type = 'kc' ORDER BY region";
                        $regions_result = mysqli_query($koneksi, $sql_regions);
                        while ($region = mysqli_fetch_assoc($regions_result)) {
                            $selected = ($_POST['pilih-uker'] == $region['region']) ? 'selected' : '';
                            echo "<option value='{$region['region']}' $selected>{$region['region']}</option>";
                        }
                        ?>
                    </select>
                </div>
            </div>
            <div class="text-center mt-4">
                <button type="button" id="startGeneration" class="btn-generate">
                    <i class="fas fa-play"></i> Mulai Generate Cache
                </button>
            </div>
        </form>
    </div>

    <!-- Progress Container -->
    <div id="progressContainer" class="progress-container hidden">
        <div class="progress-header">
            <h3><i class="fas fa-cogs fa-spin"></i> Sedang Memproses Data...</h3>
            <p>Mohon tunggu, proses ini mungkin memakan waktu beberapa menit</p>
        </div>

        <div class="progress-bar-container">
            <div id="progressBar" class="progress-bar" style="width: 0%;">
                <span id="progressText">0%</span>
            </div>
        </div>

        <div class="status-container">
            <div class="status-card">
                <div id="totalBranches" class="status-number">0</div>
                <div>Total Branches</div>
            </div>
            <div class="status-card">
                <div id="totalPeriods" class="status-number">0</div>
                <div>Total Periods</div>
            </div>
            <div class="status-card">
                <div id="processedCount" class="status-number">0</div>
                <div>Processed</div>
            </div>
            <div class="status-card">
                <div id="successCount" class="status-number">0</div>
                <div>Success</div>
            </div>
            <div class="status-card">
                <div id="errorCount" class="status-number">0</div>
                <div>Errors</div>
            </div>
            <div class="status-card">
                <div id="currentBranch" class="status-number">-</div>
                <div>Current Branch</div>
            </div>
        </div>

        <div class="console-output" id="consoleOutput">
            <div>Console Output:</div>
            <div>Waiting for process to start...</div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <div id="successMessage" class="success-message hidden">
        <i class="fas fa-check-circle"></i>
        <span id="successText">Generation completed successfully!</span>
        <div class="mt-3">
            <button class="btn btn-light btn-sm" onclick="viewCacheData()">
                <i class="fas fa-table"></i> View Cache Data
            </button>
            <button class="btn btn-light btn-sm" onclick="downloadReport()">
                <i class="fas fa-download"></i> Download Report
            </button>
        </div>
    </div>

    <div id="errorMessage" class="error-message hidden">
        <i class="fas fa-exclamation-triangle"></i>
        <span id="errorText">An error occurred during generation.</span>
        <div class="mt-3">
            <button class="btn btn-light btn-sm" onclick="showErrorDetails()">
                <i class="fas fa-info-circle"></i> Show Details
            </button>
            <button class="btn btn-light btn-sm" onclick="retryGeneration()">
                <i class="fas fa-redo"></i> Retry
            </button>
        </div>
    </div>

    <!-- Cache Status Panel -->
    <div class="form-container mt-4">
        <h5><i class="fas fa-database"></i> Cache Status</h5>
        <div class="row" id="cacheStatusPanel">
            <div class="col-md-3">
                <div class="text-center">
                    <div class="h4 text-primary" id="cacheRecords">-</div>
                    <small>Total Records</small>
                </div>
            </div>
            <div class="col-md-3">
                <div class="text-center">
                    <div class="h4 text-success" id="cachePeriods">-</div>
                    <small>Periods</small>
                </div>
            </div>
            <div class="col-md-3">
                <div class="text-center">
                    <div class="h4 text-info" id="cacheBranches">-</div>
                    <small>Branches</small>
                </div>
            </div>
            <div class="col-md-3">
                <div class="text-center">
                    <div class="h4 text-warning" id="lastUpdate">-</div>
                    <small>Last Update</small>
                </div>
            </div>
        </div>
        <div class="text-center mt-3">
            <button class="btn btn-outline-primary btn-sm" onclick="refreshCacheStatus()">
                <i class="fas fa-sync"></i> Refresh Status
            </button>
            <button class="btn btn-outline-danger btn-sm" onclick="clearCache()">
                <i class="fas fa-trash"></i> Clear Cache
            </button>
        </div>
    </div>

    <!-- Estimated Time Panel -->
    <div id="estimatedTimePanel" class="form-container mt-4 hidden">
        <h6><i class="fas fa-clock"></i> Estimated Processing Time</h6>
        <div class="row text-center">
            <div class="col-md-4">
                <div class="h5 text-info" id="estimatedTime">-</div>
                <small>Estimated Total Time</small>
            </div>
            <div class="col-md-4">
                <div class="h5 text-success" id="elapsedTime">00:00:00</div>
                <small>Elapsed Time</small>
            </div>
            <div class="col-md-4">
                <div class="h5 text-warning" id="remainingTime">-</div>
                <small>Remaining Time</small>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript for Progress Handling -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
<script>
    let isGenerating = false;
    let totalOperations = 0;
    let currentOperation = 0;

    document.getElementById('startGeneration').addEventListener('click', function() {
        if (isGenerating) {
            return;
        }

        const year = document.getElementById('yearSelect').value;
        const uker = document.getElementById('ukerSelect').value;

        startGeneration(year, uker);
    });

    function startGeneration(year, uker) {
        isGenerating = true;

        // Show progress container
        document.getElementById('progressContainer').classList.remove('hidden');
        document.getElementById('successMessage').classList.add('hidden');
        document.getElementById('errorMessage').classList.add('hidden');

        // Disable button
        const button = document.getElementById('startGeneration');
        button.disabled = true;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';

        // Reset counters
        resetCounters();

        // Add initial console message
        addConsoleMessage('Starting NEW NPL cache generation...');
        addConsoleMessage(`Year: ${year}, Unit Kerja: ${uker || 'All'}`);

        // Start the generation process
        generateCache(year, uker);
    }

    function generateCache(year, uker) {
        const formData = new FormData();
        formData.append('action', 'generate');
        formData.append('year', year);
        formData.append('uker', uker);

        fetch('generate_new_npl_api.php', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.body.getReader();
            })
            .then(reader => {
                const decoder = new TextDecoder();

                function readStream() {
                    return reader.read().then(({
                        done,
                        value
                    }) => {
                        if (done) {
                            completeGeneration();
                            return;
                        }

                        const chunk = decoder.decode(value);
                        const lines = chunk.split('\n');

                        lines.forEach(line => {
                            if (line.trim()) {
                                try {
                                    const data = JSON.parse(line);
                                    handleStreamData(data);
                                } catch (e) {
                                    // If not JSON, treat as console message
                                    if (line.trim()) {
                                        addConsoleMessage(line);
                                    }
                                }
                            }
                        });

                        return readStream();
                    });
                }

                return readStream();
            })
            .catch(error => {
                console.error('Error:', error);
                showError('Error during generation: ' + error.message);
                completeGeneration();
            });
    }

    function handleStreamData(data) {
        switch (data.type) {
            case 'init':
                totalOperations = data.total;
                document.getElementById('totalBranches').textContent = data.branches;
                document.getElementById('totalPeriods').textContent = data.periods;
                addConsoleMessage(`Initialized: ${data.branches} branches × ${data.periods} periods = ${data.total} operations`);
                break;

            case 'progress':
                currentOperation = data.current;
                updateProgress(data.current, data.total);
                document.getElementById('processedCount').textContent = data.current;
                document.getElementById('successCount').textContent = data.success;
                document.getElementById('errorCount').textContent = data.errors;
                document.getElementById('currentBranch').textContent = data.current_branch || '-';
                break;

            case 'message':
                addConsoleMessage(data.message, data.level);
                break;

            case 'branch_complete':
                addConsoleMessage(`✓ ${data.branch}: OS=${data.os}, DEB=${data.deb}`, 'success');
                break;

            case 'branch_error':
                addConsoleMessage(`✗ ${data.branch}: ${data.error}`, 'error');
                break;

            case 'period_complete':
                addConsoleMessage(`Completed period: ${data.period}`, 'info');
                break;
        }
    }

    function updateProgress(current, total) {
        const percentage = Math.round((current / total) * 100);
        const progressBar = document.getElementById('progressBar');
        const progressText = document.getElementById('progressText');

        progressBar.style.width = percentage + '%';
        progressText.textContent = `${current}/${total} (${percentage}%)`;
    }

    function addConsoleMessage(message, level = 'info') {
        const console = document.getElementById('consoleOutput');
        const timestamp = new Date().toLocaleTimeString();
        const levelIcon = {
            'info': '💡',
            'success': '✅',
            'error': '❌',
            'warning': '⚠️'
        };

        const icon = levelIcon[level] || '📝';
        const formattedMessage = `[${timestamp}] ${icon} ${message}`;

        const messageDiv = document.createElement('div');
        messageDiv.textContent = formattedMessage;
        messageDiv.style.color = level === 'error' ? '#ff6b6b' :
            level === 'success' ? '#00c851' :
            level === 'warning' ? '#ffbb33' : '#00ff00';

        console.appendChild(messageDiv);
        console.scrollTop = console.scrollHeight;
    }

    function resetCounters() {
        document.getElementById('totalBranches').textContent = '0';
        document.getElementById('totalPeriods').textContent = '0';
        document.getElementById('processedCount').textContent = '0';
        document.getElementById('successCount').textContent = '0';
        document.getElementById('errorCount').textContent = '0';
        document.getElementById('currentBranch').textContent = '-';

        // Clear console
        const console = document.getElementById('consoleOutput');
        console.innerHTML = '<div>Console Output:</div>';
    }

    function completeGeneration() {
        isGenerating = false;

        // Re-enable button
        const button = document.getElementById('startGeneration');
        button.disabled = false;
        button.innerHTML = '<i class="fas fa-play"></i> Mulai Generate Cache';

        // Show completion message
        const successCount = parseInt(document.getElementById('successCount').textContent);
        const errorCount = parseInt(document.getElementById('errorCount').textContent);

        if (errorCount === 0) {
            showSuccess(`Generation completed successfully! ${successCount} records processed.`);
        } else {
            showSuccess(`Generation completed with ${errorCount} errors. ${successCount} records processed successfully.`);
        }

        addConsoleMessage('=== GENERATION COMPLETED ===', 'success');
        addConsoleMessage(`Final stats: ${successCount} success, ${errorCount} errors`, 'info');
    }

    function showSuccess(message) {
        const successDiv = document.getElementById('successMessage');
        document.getElementById('successText').textContent = message;
        successDiv.classList.remove('hidden');

        // Auto-hide after 10 seconds
        setTimeout(() => {
            successDiv.classList.add('hidden');
        }, 10000);
    }

    function showError(message) {
        const errorDiv = document.getElementById('errorMessage');
        document.getElementById('errorText').textContent = message;
        errorDiv.classList.remove('hidden');

        // Auto-hide after 10 seconds
        setTimeout(() => {
            errorDiv.classList.add('hidden');
        }, 10000);
    }

    // Additional functions for enhanced features
    let startTime = null;
    let timerInterval = null;

    function viewCacheData() {
        window.open('view_cache_data.php', '_blank');
    }

    function downloadReport() {
        const year = document.getElementById('yearSelect').value;
        const uker = document.getElementById('ukerSelect').value;
        window.open(`download_cache_report.php?year=${year}&uker=${uker}`, '_blank');
    }

    function showErrorDetails() {
        // Show detailed error information in a modal or new window
        alert('Error details will be shown here. Check console for more information.');
    }

    function retryGeneration() {
        const year = document.getElementById('yearSelect').value;
        const uker = document.getElementById('ukerSelect').value;
        startGeneration(year, uker);
    }

    function refreshCacheStatus() {
        fetch('generate_new_npl_api.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=cache_status'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('cacheRecords').textContent = data.stats.total_records || '0';
                    document.getElementById('cachePeriods').textContent = data.stats.total_periods || '0';
                    document.getElementById('cacheBranches').textContent = data.stats.total_branches || '0';
                    document.getElementById('lastUpdate').textContent = data.stats.last_update || 'Never';
                }
            })
            .catch(error => {
                console.error('Error refreshing cache status:', error);
            });
    }

    function clearCache() {
        if (!confirm('Are you sure you want to clear all cache data? This action cannot be undone.')) {
            return;
        }

        fetch('generate_new_npl_api.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=clear_cache'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Cache cleared successfully!');
                    refreshCacheStatus();
                } else {
                    alert('Error clearing cache: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error clearing cache:', error);
                alert('Error clearing cache. Check console for details.');
            });
    }

    function estimateProcessingTime(totalOperations) {
        // Estimate based on average processing time per operation (approximately 2 seconds per operation)
        const avgTimePerOperation = 2; // seconds
        const totalSeconds = totalOperations * avgTimePerOperation;

        const hours = Math.floor(totalSeconds / 3600);
        const minutes = Math.floor((totalSeconds % 3600) / 60);
        const seconds = totalSeconds % 60;

        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }

    function updateElapsedTime() {
        if (!startTime) return;

        const now = new Date();
        const elapsed = Math.floor((now - startTime) / 1000);

        const hours = Math.floor(elapsed / 3600);
        const minutes = Math.floor((elapsed % 3600) / 60);
        const seconds = elapsed % 60;

        document.getElementById('elapsedTime').textContent =
            `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }

    function updateRemainingTime(current, total) {
        if (!startTime || current === 0) return;

        const now = new Date();
        const elapsed = (now - startTime) / 1000;
        const avgTimePerOperation = elapsed / current;
        const remaining = (total - current) * avgTimePerOperation;

        const hours = Math.floor(remaining / 3600);
        const minutes = Math.floor((remaining % 3600) / 60);
        const seconds = Math.floor(remaining % 60);

        document.getElementById('remainingTime').textContent =
            `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }

    // Enhanced startGeneration function
    function startGenerationEnhanced(year, uker) {
        startTime = new Date();

        // Show estimated time panel
        document.getElementById('estimatedTimePanel').classList.remove('hidden');

        // Start timer
        timerInterval = setInterval(updateElapsedTime, 1000);

        // Call original function
        startGeneration(year, uker);
    }

    // Enhanced handleStreamData function
    function handleStreamDataEnhanced(data) {
        handleStreamData(data);

        // Additional handling for time estimation
        if (data.type === 'init') {
            const estimatedTime = estimateProcessingTime(data.total);
            document.getElementById('estimatedTime').textContent = estimatedTime;
        }

        if (data.type === 'progress') {
            updateRemainingTime(data.current, data.total);
        }
    }

    // Enhanced completeGeneration function
    function completeGenerationEnhanced() {
        if (timerInterval) {
            clearInterval(timerInterval);
            timerInterval = null;
        }

        // Hide estimated time panel
        setTimeout(() => {
            document.getElementById('estimatedTimePanel').classList.add('hidden');
        }, 5000);

        // Refresh cache status
        refreshCacheStatus();

        // Call original function
        completeGeneration();
    }

    // Add some visual enhancements
    document.addEventListener('DOMContentLoaded', function() {
        // Load cache status on page load
        refreshCacheStatus();

        // Add loading animation to progress bar
        const style = document.createElement('style');
        style.textContent = `
        @keyframes pulse {
            0% { box-shadow: 0 4px 15px rgba(0,200,81,0.3); }
            50% { box-shadow: 0 4px 25px rgba(0,200,81,0.6); }
            100% { box-shadow: 0 4px 15px rgba(0,200,81,0.3); }
        }

        .progress-bar {
            animation: pulse 2s infinite;
        }

        .fa-spin {
            animation: fa-spin 1s infinite linear;
        }

        .status-card:hover {
            transform: translateY(-2px);
            transition: transform 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-1px);
            transition: transform 0.2s ease;
        }
    `;
        document.head.appendChild(style);

        // Override the original startGeneration call
        document.getElementById('startGeneration').removeEventListener('click', function() {});
        document.getElementById('startGeneration').addEventListener('click', function() {
            if (isGenerating) {
                return;
            }

            const year = document.getElementById('yearSelect').value;
            const uker = document.getElementById('ukerSelect').value;

            startGenerationEnhanced(year, uker);
        });
    });
</script>