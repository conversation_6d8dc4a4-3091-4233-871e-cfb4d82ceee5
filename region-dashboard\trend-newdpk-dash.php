<?php

if ($_POST['pilih-uker'] == '') {
    $uker = get_uker_region_kc('F');
    $uker2 = get_uker_region_kc('F');
} else {
    $uker = get_list_unit_active($_POST['pilih-uker']);
    $uker2 = get_list_unit_active($_POST['pilih-uker']);
}

if ($_GET['view'] == 'unit') {
    $uker = get_list_unit_active_f();
    $uker2 = get_list_unit_active_f();
}

?>

<?php


//$arr_akhir_bln = array('2024-01-31', '2024-02-29', '2024-03-31', '2024-04-30', '2024-05-31', '2024-06-30', '2024-07-31', '2024-08-31', '2024-09-30', '2024-10-31', '2024-11-30', '2024-12-31');

$satuan = 1000000;
$month = date('m');
$year = date('Y');

//

if ($_POST['year'] == '') {
    $year = date('Y');
} else {
    $year = $_POST['year'];
}

if (isset($year)) {
    $tahun = intval($year);
    $arr_akhir_bln = [];

    for ($bulan = 1; $bulan <= 12; $bulan++) {
        $tgl = date("Y-m-t", strtotime("$tahun-$bulan-01"));
        $arr_akhir_bln[] = $tgl;
    }
}

$z = 3;
for ($i = 1; $i <= $hari; $i++) {
    $arr_i[$i] = $z;
    $z++;
}
$series_hari = implode(',', $arr_i);

?>
<!-- DataTables CSS -->
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.bootstrap5.min.css">

<!-- DataTables Core JS -->
<script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>

<!-- DataTables Buttons JS -->
<script src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.bootstrap5.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.2.7/pdfmake.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.2.7/vfs_fonts.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.html5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.print.min.js"></script>


<!-- fungsi datatable -->

<script>
    $(document).ready(function() {
        // Add small delay to ensure all resources are loaded
        setTimeout(function() {
            try {
                // Check if jQuery is loaded
                if (typeof $ === 'undefined') {
                    console.error('jQuery is not loaded');
                    throw new Error('jQuery is not loaded');
                }

                // Check if table exists
                if ($('#tabel-data').length === 0) {
                    console.error('Table #tabel-data not found');
                    throw new Error('Table #tabel-data not found');
                }

                // Check if DataTables is loaded
                if (typeof $.fn.DataTable === 'undefined') {
                    console.error('DataTables library not loaded');
                    throw new Error('DataTables library not loaded');
                }

                // Count actual columns in table
                var headerCols = $('#tabel-data thead tr:last th').length;
                var footerCols = $('#tabel-data tfoot th').length;
                var bodyCols = $('#tabel-data tbody tr:first td').length;

                console.log('Header columns:', headerCols);
                console.log('Footer columns:', footerCols);
                console.log('Body columns:', bodyCols);

                // Check for column mismatch
                if (headerCols !== footerCols) {
                    console.warn('Column count mismatch: Header=' + headerCols + ', Footer=' + footerCols);
                }

                // Initialize DataTable with basic configuration first
                var table = $('#tabel-data').DataTable({
                    paging: false,
                    searching: true,
                    ordering: true,
                    info: true,
                    autoWidth: false,
                    responsive: true,
                    language: {
                        search: "Search:",
                        info: "Showing _START_ to _END_ of _TOTAL_ entries",
                        infoEmpty: "No data available",
                        zeroRecords: "No matching records found"
                    },
                    footerCallback: function(row, data, start, end, display) {
                        try {
                            var api = this.api();
                            var nb_cols = api.columns().nodes().length;

                            // Fungsi konversi string ke angka
                            var intVal = function(i) {
                                if (typeof i === 'string') {
                                    var cleaned = i.replace(/[\$,\s]/g, '');
                                    return isNaN(cleaned) ? 0 : parseFloat(cleaned);
                                }
                                return typeof i === 'number' ? i : 0;
                            };

                            // Loop mulai dari kolom ke-2 (index 2)
                            for (var j = 2; j < nb_cols; j++) {
                                try {
                                    var pageTotal = api
                                        .column(j, {
                                            page: 'current'
                                        })
                                        .data()
                                        .reduce(function(a, b) {
                                            return intVal(a) + intVal(b);
                                        }, 0);

                                    // Format ke en-US: koma ribuan, titik desimal
                                    var formattedTotal = new Intl.NumberFormat('en-US', {
                                        minimumFractionDigits: 0,
                                        maximumFractionDigits: 0
                                    }).format(pageTotal);

                                    $(api.column(j).footer()).html('<strong>' + formattedTotal + '</strong>');
                                } catch (colError) {
                                    console.warn('Error processing column ' + j + ':', colError);
                                }
                            }
                        } catch (footerError) {
                            console.warn('Error in footer callback:', footerError);
                        }
                    },

                    dom: 'Bfrtip',
                    buttons: [{
                            extend: 'copyHtml5',
                            text: 'Copy',
                            footer: true
                        },
                        {
                            extend: 'excelHtml5',
                            text: 'Excel',
                            footer: true,
                            title: 'Trend NEW NPL Data'
                        },
                        {
                            extend: 'csvHtml5',
                            text: 'CSV',
                            footer: true
                        }
                    ],

                    // Terapkan class text-end ke kolom angka (mulai dari kolom index 2 ke atas)
                    columnDefs: [{
                            targets: [0], // BC column
                            className: "text-center"
                        },
                        {
                            targets: [1], // Branch name column
                            className: "text-start"
                        },
                        {
                            targets: [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25], // Data columns
                            className: "text-end",
                            render: function(data, type, row) {
                                if (type === 'display' && data && !isNaN(data)) {
                                    return new Intl.NumberFormat('en-US', {
                                        minimumFractionDigits: 0,
                                        maximumFractionDigits: 0
                                    }).format(data);
                                }
                                return data;
                            }
                        }
                    ],

                    initComplete: function() {
                        console.log('DataTable initialized successfully');

                        // Add custom styling
                        $('.dataTables_filter input').addClass('form-control').attr('placeholder', 'Search branches...');
                        $('.dataTables_length select').addClass('form-select');

                        // Success message
                        console.log('Advanced table features loaded successfully');
                    }
                });

                console.log('DataTable configuration completed');

            } catch (error) {
                console.error('Error initializing DataTable:', error);
                console.error('Error details:', error.message);
                console.error('Error stack:', error.stack);

                // Fallback: Apply basic styling if DataTable fails
                $('#tabel-data').addClass('table table-striped table-bordered');
                $('#tabel-data thead').addClass('table-danger');
                $('#tabel-data tfoot').addClass('table-danger');

                // Show error message with more details
                $('<div class="alert alert-warning alert-dismissible fade show" role="alert">' +
                    '<i class="fas fa-exclamation-triangle"></i> ' +
                    '<strong>DataTable Error:</strong> Advanced table features could not be loaded. ' +
                    '<br><small>Error: ' + error.message + '</small>' +
                    '<br><small>Basic table view is shown instead.</small>' +
                    '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>' +
                    '</div>').insertBefore('#tabel-data');
            }
        }, 500); // 500ms delay to ensure all resources are loaded
    });

    // Additional fallback check after page load
    $(window).on('load', function() {
        setTimeout(function() {
            // Check if DataTable was successfully initialized
            if (!$.fn.DataTable.isDataTable('#tabel-data')) {
                console.warn('DataTable not initialized, applying basic enhancements');

                // Apply basic table enhancements
                $('#tabel-data').addClass('table table-striped table-bordered table-hover');
                $('#tabel-data thead').addClass('table-danger');
                $('#tabel-data tfoot').addClass('table-danger');

                // Add basic search functionality
                if ($('#basic-search').length === 0) {
                    $('<div class="mb-3">' +
                        '<label for="basic-search" class="form-label">Search:</label>' +
                        '<input type="text" id="basic-search" class="form-control" placeholder="Search in table...">' +
                        '</div>').insertBefore('#tabel-data');

                    $('#basic-search').on('keyup', function() {
                        var value = $(this).val().toLowerCase();
                        $('#tabel-data tbody tr').filter(function() {
                            $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
                        });
                    });
                }

                // Show basic table message
                if ($('.alert-info').length === 0) {
                    $('<div class="alert alert-info">' +
                        '<i class="fas fa-info-circle"></i> ' +
                        'Basic table view with search functionality is active.' +
                        '</div>').insertBefore('#tabel-data');
                }
            }
        }, 1000);
    });
</script>

<?php include 'menu-trends.php'; ?>
<h5 class="text-center">Trend NEW NPL (Rp)</h5>
<div class="text-center">
    <a href="?mode=trend_newnpl&view=unit"><button class="button btn-warning text-dark">Unit View</button></a>
</div>
<script>
    document.title = 'Series NEW NPL <?php echo tgl_periode($tgl_max); ?>'
</script>
<p class="text-center">
<div class="container-fluid">
    <form action="" method="post">
        <input type="hidden" name="pilih-uker" value="<?php echo $_POST['pilih-uker'] ?>">
        <label>Pilih Tahun Periode : </label>
        <select name="year" onchange="this.form.submit()">
            <?php
            if ($_POST['year'] == '') {
                $_POST['year'] = date('Y');
            }

            for ($y = ($year - 1); $y <= ($year + 1); $y++) {
                if ($y == $_POST['year']) {
                    $sl = 'selected';
                } else {
                    $sl = '';
                }
            ?>
                <option value="<?php echo $y ?>" <?php echo $sl ?>><?php echo $y; ?></option>
            <?php } ?>
        </select>
    </form>
</div>
</p>

<table id="tabel-data" class="table table-striped table-bordered" style="width:100%" data-page-length='50'>
    <thead class="table-danger">
        <tr>
            <th rowspan="2">BC</th>
            <th rowspan="2">Branch Office</th>

            <?php
            foreach ($arr_akhir_bln as $ab) {
            ?>
                <th colspan="2"><?php echo tgl_periode($ab); ?></th>
            <?php } ?>

        </tr>
        <tr>
            <?php
            foreach ($arr_akhir_bln as $ab) {
            ?>
                <th>Debitur</th>
                <th>Outstanding</th>
            <?php } ?>
        </tr>
    </thead>
    <tfoot class="table-danger">
        <th></th>
        <th>TOTAL</th>

        <?php foreach ($arr_akhir_bln as $ab) { ?>
            <th></th>
            <th></th>
        <?php } ?>
    </tfoot>
    <tbody>
        <?php
        // $sql_max_real = 'select max(periode) from l1133';
        // $rs_max = mysqli_query($koneksi, $sql_max_real);
        // $tgl_max = mysqli_fetch_array($rs_max);

        if ($_GET['view'] == 'unit') {
            $branch = "branch_code";
        } else {
            $branch = "main_branch";
        }

        while ($dat_u = mysqli_fetch_array($uker)) {
            $i++;
        ?>
            <tr>
                <td><?php echo $dat_u['kode_uker'] ?></td>
                <td><?php echo $dat_u['nama_uker'] ?></td>

                <?php
                foreach ($arr_akhir_bln as $ab) {
                    $periode_ = $ab;
                    $uker_type = cek_branch2($dat_u['kode_uker']);
                    if ($uker_type == 'ro') {
                        $branch = "main_branch";
                    } else if ($uker_type == 'kc') {
                        $branch = "main_branch";
                    } else if ($uker_type == 'un') {
                        $branch = "branch_code";
                    }
                    $sql_new_dpk = "select SUM(new_dpk_os) as os, SUM(new_dpk_deb) as deb from new_dpk_cache where $branch = '" . $dat_u['kode_uker'] . "' and periode = '$periode_' and branch_code <> main_branch";
                    // echo $sql_new_dpk;
                    $rs_new_dpk = mysqli_query($koneksi, $sql_new_dpk);
                    $npl_periode = mysqli_fetch_array($rs_new_dpk);

                    // $npl_periode = new_dpk($dat_u['kode_uker'], $periode_);


                ?>

                    <td><?php  // echo $mtd_npl;
                        echo $npl_periode['deb'];
                        ?></td>
                    <td><?php  // echo $mtd_npl;
                        echo $npl_periode['os'];
                        ?></td>
                <?php } ?>
            </tr>
        <?php } ?>
    </tbody>
</table>