<?php
session_start();

// print_r($_POST);
$auth_role = array('super354', 'god', 'area');
function sanitize_input($input)
{
    $input = trim($input);
    $input = stripslashes($input);
    $input = htmlspecialchars($input);
    return $input;
}

function getDates($month, $year)
{
    // Format tanggal awal
    $tanggal_awal = date("Y-m-d", strtotime("$year-$month-01"));

    // Format tanggal akhir (tanggal 1 bulan berikutnya)
    $tanggal_akhir = date("Y-m-d", strtotime("$tanggal_awal +1 month"));

    // Return sebagai array
    return [
        "tanggal_awal" => $tanggal_awal,
        "tanggal_akhir" => $tanggal_akhir
    ];
}



if (isset($_SESSION['role']) && !in_array($_SESSION['role'], $auth_role)) {
    // Kode yang akan dijalankan jika role tidak ada dalam array
    die('NOT authorized!!');
}

$view = htmlspecialchars($_GET['view_unit']);

if ($view == '') {
    if ($_POST['pilih-uker'] == '') {
        if ($_SESSION['role'] == 'god') {
            $uker = get_uker_region_kc($_SESSION['kodeuker']);
        } else if ($_POST['pilih-uker'] == '' && $_SESSION['role'] == 'area' && $_GET['pilih-uker'] == '') {
            $uker = get_uker_region_kc($_SESSION['kodeuker']);
            $unitview = "on";
        } else {
            $uker = get_list_unit_active($_SESSION['kodeuker']);
            $unitview = "on";
        }
    } else {
        $uker = get_list_unit_active($_POST['pilih-uker']);
        $uker2 = get_list_unit_active($_POST['pilih-uker']);
        $unitview = "on";
    }
    // $uker = get_list_unit_active_f();
    // $uker2 = get_list_unit_active_f();
} else if ($view == 'on') {
    $uker = get_list_unit_active_f();
}
//$satuan = 1000000;
$satuan = 1;
$month = $_POST['month'] ?? date('m');
$year = $_POST['year'] ?? date('Y');

$sql_max_real = 'select max(periode) from lw321';
$rs_max = mysqli_query($koneksi, $sql_max_real);
$tgl_max = mysqli_fetch_array($rs_max);

$tanggalMulai = date('d');  // Tanggal mulai

$hk = getHariKerjaRange(1, cal_days_in_month(CAL_GREGORIAN, $month, $year), $month, $year, $liburNasional);


$total_hari_kerja = $hk['total'];

// Hari kerja yang sudah dilalui (misalnya sampai tanggal 12 April)
$hariKerjaTerlalui = $hk['terlalui'];

// Sisa hari kerja
$sisaHariKerja = $hk['sisa'];

// echo $total_hari_kerja . " " . $hariKerjaTerlalui . " " . $sisaHariKerja;

//$total_hari_kerja = HariKerja($month, $year);

$tanggalHariIni = new DateTime();
$month_date_hari_ini = date_format($tanggalHariIni, 'Y-m');
if ($month_date_hari_ini == ($_POST['year'] . '-' . $_POST['month'])) {
    $tgl_hari_ini = 1;
}

if (($_POST['month'] == '' || $_POST['year'] == '') && ($tgl_hari_ini != '1')) {
    $periode_date = date('Y') . '-' . date('m');
    $month = date('m');
    $year = date('Y');
    $sisa_hari_kerja = hitungSisaHariKerja($tanggalMulai, $month, $year);
    $sudahHariKerja = $total_hari_kerja - $sisa_hari_kerja;
} else {
    $periode_date = $_POST['year'] . '-' . $_POST['month'];
    $month = $_POST['month'];
    $year = $_POST['year'];
    $tgl_max[0] = getTanggalAkhirBulan($month, $year);
    $datedprev = getTanggalAkhirBulanSebelumnya($year . '-' . $month . '-01');
    $sudahHariKerja = $total_hari_kerja;
};

?>
<h5 class="text-center">Kinerja Realisasi Pinjaman ( Harian ) </h5>

<?php
echo '
<div class="container d-flex justify-content-center mt-3">
    <div class="d-flex gap-2" style="max-width: 460px;">
        <div class="card shadow-sm border-0 rounded-3 text-center p-2 flex-fill" style="background-color: #e8f0fe;">
            <div class="text-muted small">
                <i class="bi bi-calendar-range me-1"></i>Total Hari Kerja
            </div>
            <div class="fw-semibold text-primary fs-6">' . $total_hari_kerja . '</div>
        </div>
        <div class="card shadow-sm border-0 rounded-3 text-center p-2 flex-fill" style="background-color: #e6f4ea;">
            <div class="text-muted small">
                <i class="bi bi-calendar-check me-1"></i>Sisa Hari Kerja
            </div>
            <div class="fw-semibold text-success fs-6">' . $sisaHariKerja . '</div>
        </div>
        <div class="card shadow-sm border-0 rounded-3 text-center p-2 flex-fill" style="background-color: #f0f4f8;">
            <div class="text-muted small">
                <i class="bi bi-calendar2-week me-1"></i>HK Telah Dilalui
            </div>
            <div class="fw-semibold text-info fs-6">' . $hariKerjaTerlalui . '</div>
        </div>
    </div>
</div>';

function getLastDayOfMonth($year, $month)
{
    return date('Y-m-t', strtotime("$year-$month-01"));
}

$tgl_akhir_bulan = getLastDayOfMonth($year, $month);

$hari = cal_days_in_month(CAL_GREGORIAN, $month, $year);
$arr_month = array('Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni', 'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember');


$z = 12;
$arr_i = array();
for ($i = 1; $i <= $hari; $i++) {
    $arr_i[$i] = $z;
    $z++;
}

$series_hari = implode(',', $arr_i);

$y = 3;
for ($i = 1; $i <= $hari; $i++) {
    $arr_z[$i] = $y;
    $y++;
}

$series_hari2 = implode(',', $arr_z);

$start_date = $_POST['month'];
$end_date = $tgl_max[0];

?>
<!-- jQuery -->
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>


<!-- Bootstrap 5 CSS -->
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">

<!-- DataTables CSS -->
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/fixedcolumns/4.3.0/css/fixedColumns.bootstrap5.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.bootstrap5.min.css">

<!-- Font Awesome for Icons -->
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

<!-- jQuery -->
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

<!-- Bootstrap 5 Bundle JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

<!-- DataTables JS -->
<script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
<script src="https://cdn.datatables.net/fixedcolumns/4.3.0/js/dataTables.fixedColumns.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.bootstrap5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.html5.min.js"></script>

<!-- JSZip for Excel Export -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>

<!-- Simple CSS for Fixed Columns -->
<style>
    /* Basic table styling */
    #tabel-data {
        width: 100% !important;
        border-collapse: collapse;
    }

    #tabel-data th,
    #tabel-data td {
        border: 1px solid #dee2e6;
        padding: 8px;
        text-align: center;
        white-space: nowrap;
    }

    #tabel-data thead th {
        background-color: #343a40;
        color: white;
        font-weight: 600;
    }

    /* Fixed columns styling */
    .dtfc-fixed-left {
        background-color: #f8f9fa;
        border-right: 2px solid #007bff;
        box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
    }

    .dtfc-fixed-left thead th {
        background-color: #343a40;
        color: white;
    }

    .dtfc-fixed-left tbody td {
        background-color: white;
        font-weight: 500;
    }

    /* Scrolling */
    .dataTables_scrollBody {
        overflow-x: auto;
    }

    /* Buttons */
    .dt-buttons {
        margin-bottom: 10px;
    }

    .dt-button {
        margin-right: 5px;
    }
</style>






<script>
    $(document).ready(function() {
        console.log('Initializing DataTable...');

        // Simple DataTable initialization with Fixed Columns
        var table = $('#tabel-data').DataTable({
            // Basic settings
            paging: false,
            searching: false,
            ordering: true,
            info: true,

            // Scrolling
            scrollX: true,
            scrollY: 600,
            scrollCollapse: true,

            // Fixed columns - simple configuration
            fixedColumns: {
                leftColumns: 2
            },

            // Export buttons
            dom: 'Bfrtip',
            buttons: [{
                    extend: 'copy',
                    text: 'Copy',
                    className: 'btn btn-primary btn-sm'
                },
                {
                    extend: 'excel',
                    text: 'Excel',
                    className: 'btn btn-success btn-sm'
                }
            ],

            // Column definitions
            columnDefs: [{
                    targets: [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, <?php echo $series_hari; ?>],
                    className: "text-end"
                },
                {
                    targets: [0],
                    width: "80px",
                    className: "text-center"
                },
                {
                    targets: [1],
                    width: "200px",
                    className: "text-left"
                }
            ],

            // Language
            language: {
                info: "Menampilkan _TOTAL_ data",
                infoEmpty: "Tidak ada data"
            },

            // Callbacks
            initComplete: function() {
                console.log('DataTable initialized successfully');
                console.log('Fixed columns should be active now');
            }
        });

        console.log('DataTable setup complete');
    });
</script>



<script>
    document.title = 'Summary Kinerja Realisasi Pinjaman Periode <?php echo tgl_periode($tgl_max); ?>'
</script>


<p class="text-center">
<div class="container-fluid">
    <form action="" method="post">
        <input type="hidden" name="pilih-area" value="<?php echo sanitize_input($_POST['pilih-area']); ?>">
        <input type="hidden" name="pilih-uker" value="<?php echo sanitize_input($_POST['pilih-uker']); ?>">
        <input type="hidden" name="view_unit" value="<?php echo sanitize_input($_GET['view_unit']); ?>">
        <input type="hidden" name="pilih_periode" value="<?php echo sanitize_input($_GET['pilih_periode']); ?>">
        <input type="hidden" name="month" value="<?php echo sanitize_input($_GET['month']); ?>">
        <input type="hidden" name="year" value="<?php echo sanitize_input($_GET['year']); ?>">
        <label>Periode Disburse : </label> <select name="month">
            <?php
            $i = 0;
            foreach ($arr_month as $bln) {
                $i++;
                if ($_POST['month'] == '') {
                    $_POST['month'] = date('m');
                }
                if ($i == $_POST['month']) {
                    $sl = 'selected';
                } else {
                    $sl = '';
                }
            ?>
                <option value="<?php echo $i; ?>" <?php echo $sl; ?>><?php echo $bln; ?></option>
            <?php } ?>
        </select>

        <select name="year">
            <?php
            if ($_POST['year'] == '') {
                $_POST['year'] = date('Y');
            }

            for ($y = ($year - 1); $y <= $year; $y++) {
                if ($y == $_POST['year']) {
                    $sl = 'selected';
                } else {
                    $sl = '';
                }
            ?>
                <option value="<?php echo $y ?>" <?php echo $sl ?>><?php echo $y; ?></option>
            <?php } ?>
        </select>
        <input type="button" value="Refresh Data" class="btn btn-dark" onclick="this.form.submit()">
    </form>
</div>
</p>
<?php

if (trim($_GET['view_unit']) == 'on' || $unitview == 'on') {
    $branch = 'branch';
    $where_main_branch = " and main_branch = '" . $_SESSION['kodeuker'] . "'";
} else {
    $branch = 'main_branch';
}



$dates_real = getDates($month, $year);

// plafond all
$sql_uker = "SELECT 
                    $branch, 
                    SUM(plafond) AS total,
                    COUNT(norek) AS rek,
                     SUM(CASE WHEN plafond > 300000000  AND type NOT IN (select type from type_pinjaman where kategori = 'Briguna') THEN plafond ELSE 0 END) AS plaf_300_nongbt,
                    COUNT(CASE WHEN plafond > 300000000 AND type NOT IN (select type from type_pinjaman where kategori = 'Briguna') THEN 1 ELSE NULL END) AS rek_300_nongbt,
                    SUM(CASE WHEN plafond > 300000000 THEN plafond ELSE 0 END) AS plaf_300,
                    COUNT(CASE WHEN plafond > 300000000 THEN 1 ELSE NULL END) AS rek_300,
                     SUM(CASE WHEN plafond > 100000000 and plafond <= 300000000 THEN plafond ELSE 0 END) AS plaf_100,
                    COUNT(CASE WHEN plafond > 100000000 and plafond <= 300000000 THEN 1 ELSE NULL END) AS rek_100,
                    SUM(CASE WHEN plafond <= 50000000 THEN plafond ELSE 0 END) AS plaf_0,
                    COUNT(CASE WHEN plafond <= 50000000 THEN 1 ELSE NULL END) AS rek_0,
                    SUM(CASE WHEN plafond > 50000000 AND plafond <= 100000000 THEN plafond ELSE 0 END) AS plaf_50,
                    COUNT(CASE WHEN plafond > 50000000 AND plafond <= 100000000 THEN 1 ELSE NULL END) AS rek_50
                FROM 
                    lw321
                WHERE 
                    tgl_realisasi > '" . $dates_real['tanggal_awal'] . "'
                    and tgl_realisasi < '" . $dates_real['tanggal_akhir'] . "'
                    and
                    periode = '" . $tgl_max[0] . "'
                   
                GROUP BY 
                    $branch
                ";

//echo $sql_uker;

$plafond_data[] = "";
$uker_list = mysqli_query($koneksi, $sql_uker);
while ($row = mysqli_fetch_assoc($uker_list)) {
    $plafon_data[$row[$branch]] = $row;
}


$sql_suplesi = "
    SELECT 
        $branch,
        SUM(plafond) AS total_suplesi,
        COUNT(norek) AS rek_suplesi
    FROM 
        lw321
    WHERE 
        tgl_realisasi > '$datedprev'
        AND tgl_realisasi <= '$tgl_max[0]'
        AND periode = '$tgl_max[0]'
        AND cif IN (
            SELECT DISTINCT cif 
            FROM lw321 
            WHERE periode = '$datedprev'
        )
    GROUP BY 
        $branch
";

//echo $sql_suplesi;

// Eksekusi query
$result2 = mysqli_query($koneksi, $sql_suplesi);

// Simpan hasil ke array
$suplesi_data = [];
while ($row = mysqli_fetch_assoc($result2)) {
    $suplesi_data[$row[$branch]] = $row;
}

// echo '<pre>';
// print_r($suplesi_data);
// echo '</pre>';



$sql_baru = "
    SELECT 
        $branch,
        SUM(plafond) AS total_baru ,
        COUNT(norek) AS rek_baru
    FROM 
        lw321
    WHERE 
        tgl_realisasi > '$datedprev'
        AND tgl_realisasi <= '$tgl_max[0]'
        AND periode = '$tgl_max[0]'
        AND cif NOT IN (
            SELECT DISTINCT cif 
            FROM lw321 
            WHERE periode = '$datedprev'
        )
    GROUP BY 
        $branch
";

// Eksekusi query
$result1 = mysqli_query($koneksi, $sql_baru);

///echo $sql_baru;


// Simpan hasil ke array
$baru_data = [];
while ($row = mysqli_fetch_assoc($result1)) {
    $baru_data[$row[$branch]] = $row;
}

$sql_mbm_suplesi = "SELECT mu.id_mbm, m.nama_mbm, SUM(lw.plafond) AS total_suplesi,
                 COUNT(lw.norek) AS rek_suplesi
                  FROM lw321 lw 
                  JOIN mbm_uker mu 
                  ON lw.branch = mu.id_uker 
                  JOIN mbm m 
                  ON mu.id_mbm = m.pn_mbm 
                  WHERE lw.tgl_realisasi > '$datedprev'
                  AND lw.tgl_realisasi <= '$tgl_max[0]'
                   AND lw.periode = '$tgl_max[0]'
                    AND NOT EXISTS ( SELECT 1 FROM lw321 prev WHERE prev.cif = lw.cif AND prev.periode = '$datedprev' ) 
                    GROUP BY mu.id_mbm, m.nama_mbm;";


$sql_mbm_baru = "SELECT mu.id_mbm, m.nama_mbm, SUM(lw.plafond) AS total_suplesi,
                    COUNT(lw.norek) AS rek_suplesi
                FROM lw321 lw 
                JOIN mbm_uker mu 
                ON lw.branch = mu.id_uker 
                JOIN mbm m 
                ON mu.id_mbm = m.pn_mbm 
                WHERE lw.tgl_realisasi > '$datedprev'
                AND lw.tgl_realisasi <= '$tgl_max[0]'
                AND lw.periode = '$datedprev'
                AND NOT EXISTS ( SELECT 1 FROM lw321 prev WHERE prev.cif = lw.cif AND prev.periode = '$datedprev' ) 
                GROUP BY mu.id_mbm, m.nama_mbm";

include 'menu_real.php';

if ($_SESSION['role'] == 'god') { ?>
    | <a href="?mode=realisasi&view_unit=on" class="btn btn-dark">Unit View</a> | <a href="?mode=realmbm" class="btn btn-dark">MBM View</a>
<?php } ?>


<table id="tabel-data" class="table-striped table-bordered table" style="width:100%" data-page-length='50' cellpadding="2" cellspacing="2">
    <thead class="table-dark">
        <tr>
            <th rowspan="2">BC</th>
            <th rowspan="2">Branch Office</th>
            <?php if ($_GET['view_unit'] == 'on'): ?>
                <th rowspan="2">Main Branch</th>
            <?php endif; ?>
            <th rowspan="2">Jum <br> Mantri</th>
            <th colspan="2" class="text-center">Total <br> Disburse</th>
            <th colspan="2" class="text-center">Baru</th>
            <th colspan="2" class="text-center">Suplesi</th>
            <th colspan="2" class="text-center">Plaf <br>
                0 - 50jt </th>
            <th colspan="2" class="text-center">Plaf <br>
                51 - 100jt </th>
            <th colspan="2" class="text-center">Plaf <br> > 100 - 300jt </th>
            <th colspan="2" class="text-center">Plaf <br> > 300jt </th>
            <th colspan="2" class="text-center">Plaf <br> > 300jt Non Briguna </th>
            <th rowspan="2">Ratas <br>Per Mantri</th>
            <th rowspan="2">Guidance</th>
            <th rowspan="2">GAP <BR> Guidance</th>
            <th rowspan="2"> % <BR> Guidance </th>
            <?php for ($i = 1; $i <= $hari; $i++): ?>
                <th rowspan="2"><?php echo 'Tgl ' . $i; ?></th>
            <?php endfor; ?>
        </tr>
        <tr>
            <th>Deb</th>
            <th>Rp</th>
            <th>Deb</th>
            <th>Rp</th>
            <th>Deb</th>
            <th>Rp</th>
            <th>Deb</th>
            <th>Rp</th>
            <th>Deb</th>
            <th>Rp</th>
            <th>Deb</th>
            <th>Rp</th>
            <th>Deb</th>
            <th>Rp</th>
            <th>Deb</th>
            <th>Rp</th>

        </tr>

    </thead>

    <tbody>
        <?php
        while ($dat_u = mysqli_fetch_array($uker)) {
            $type = cek_branch2($dat_u['kode_uker']);
            if ($view == 'mbm') {
                $jum_mantri = get_jml_mantri_mbm($dat_u['kode_uker']);
            } else {
                $jum_mantri = get_jumlah_mantri($dat_u['kode_uker']);
            }
            $guidance = get_guidance($dat_u['kode_uker'], $date_rka, '1');
            $guidance['target'] = $guidance['target'] * 1000000;

            // Calculate branch condition
            if ($type == 'kc') {
                $br = "main_branch ='" . $dat_u['kode_uker'] . "' and branch <> '" . $dat_u['kode_uker'] . "'";
            } elseif ($type == 'un') {
                $br = "branch = '" . $dat_u['kode_uker'] . "'";
            } elseif ($type == 'area') {
                $br = "branch IN (select kode_uker from uker where id_area = '" . $dat_u['kode_uker'] . "')";
            }


            $ratas_mantri = ($plafon_data[$dat_u['kode_uker']]['total'] / $satuan) / $jum_mantri / $hariKerjaTerlalui;

            // Store totals
            $total_data['jum_mantri'] += $jum_mantri;
            $total_data['rek_all'] += $plafon_data[$dat_u['kode_uker']]['rek'];
            $total_data['plaf_all'] += $plafon_data[$dat_u['kode_uker']]['total'] / $satuan;


            $total_data['real_baru'] += $baru_data[$dat_u['kode_uker']]['total_baru'] / $satuan;
            $total_data['rek_baru'] += $baru_data[$dat_u['kode_uker']]['rek_baru'];


            $total_data['real_suplesi'] += $suplesi_data[$dat_u['kode_uker']]['total_suplesi'] / $satuan;
            $total_data['rek_suplesi'] += $suplesi_data[$dat_u['kode_uker']]['rek_suplesi'];

            $total_data['rek_100'] += $plafon_data[$dat_u['kode_uker']]['rek_100'];
            $total_data['plaf_100'] += $plafon_data[$dat_u['kode_uker']]['plaf_100'] / $satuan;

            $total_data['rek_300'] += $plafon_data[$dat_u['kode_uker']]['rek_300'];
            $total_data['plaf_300'] += $plafon_data[$dat_u['kode_uker']]['plaf_300'] / $satuan;

            $total_data['rek_300_nongbt'] += $plafon_data[$dat_u['kode_uker']]['rek_300_nongbt'];
            $total_data['plaf_300_nongbt'] += $plafon_data[$dat_u['kode_uker']]['plaf_300_nongbt'] / $satuan;

            $total_data['plaf_0'] += $plafon_data[$dat_u['kode_uker']]['plaf_0'] / $satuan;
            $total_data['rek_0'] += $plafon_data[$dat_u['kode_uker']]['rek_0'];

            $total_data['plaf_50'] += $plafon_data[$dat_u['kode_uker']]['plaf_50'] / $satuan;
            $total_data['rek_50'] += $plafon_data[$dat_u['kode_uker']]['rek_50'];

            $total_data['guidance'] += $guidance['target'];
        ?>
            <tr>
                <td><?php echo htmlspecialchars($dat_u['kode_uker']); ?></td>
                <td><?php echo htmlspecialchars($dat_u['nama_uker']); ?></td>
                <?php if ($_GET['view_unit'] == 'on'): ?>
                    <td><?php
                        $main_b = get_main_branch($dat_u['kode_uker']);
                        echo htmlspecialchars(get_nama_uker($main_b, $koneksi));
                        ?></td>
                <?php endif; ?>
                <td><?php echo number_format($jum_mantri); ?></td>
                <td><?php echo $plafon_data[$dat_u['kode_uker']]['rek']; ?></td>
                <td><?php echo number_format($plafon_data[$dat_u['kode_uker']]['total'] / $satuan); ?></td>
                <td><?php echo $baru_data[$dat_u['kode_uker']]['rek_baru'] ?></td>
                <td><?php echo number_format($baru_data[$dat_u['kode_uker']]['total_baru'] / $satuan); ?></td>
                <td><?php echo $suplesi_data[$dat_u['kode_uker']]['rek_suplesi']; ?></td>
                <td><?php echo number_format($suplesi_data[$dat_u['kode_uker']]['total_suplesi'] / $satuan); ?></td>
                <td><?php echo $plafon_data[$dat_u['kode_uker']]['rek_0']; ?></td>
                <td><?php echo number_format($plafon_data[$dat_u['kode_uker']]['plaf_0'] / $satuan); ?></td>
                <td><?php echo $plafon_data[$dat_u['kode_uker']]['rek_50']; ?></td>
                <td><?php echo number_format($plafon_data[$dat_u['kode_uker']]['plaf_50'] / $satuan); ?></td>
                <td><?php echo $plafon_data[$dat_u['kode_uker']]['rek_100']; ?></td>
                <td><?php echo number_format($plafon_data[$dat_u['kode_uker']]['plaf_100'] / $satuan); ?></td>
                <td><?php echo $plafon_data[$dat_u['kode_uker']]['rek_300']; ?></td>
                <td><?php echo number_format($plafon_data[$dat_u['kode_uker']]['plaf_300'] / $satuan); ?></td>
                <td><?php echo $plafon_data[$dat_u['kode_uker']]['rek_300_nongbt']; ?></td>
                <td><?php echo number_format($plafon_data[$dat_u['kode_uker']]['plaf_300_nongbt'] / $satuan); ?></td>
                <td><?php echo number_format($ratas_mantri, 2); ?></td>
                <td><?php echo number_format($guidance['target']) ?></td>
                <td><?php echo number_format(($plafon_data[$dat_u['kode_uker']]['total'] / $satuan) - $guidance['target']); ?></td>
                <td><?php echo number_format((($plafon_data[$dat_u['kode_uker']]['total'] / $satuan) / $guidance['target']) * 100, 2) ?></td>
                <?php
                for ($z = 1; $z <= $hari; $z++) {
                    $periode_ = $periode_date . '-' . $z;
                    if ($_GET['view_unit'] == '') {
                        $rz = "SELECT SUM(plafond) AS pf FROM lw321 WHERE periode = '" . $tgl_max['0'] . "' AND tgl_realisasi = '" . $periode_date . "-$z' AND main_branch = '" . $dat_u['kode_uker'] . "' AND branch IN (SELECT kode_uker FROM uker WHERE region = 'F' AND uker_type = 'un')";
                    } else {
                        $rz = "SELECT SUM(plafond) AS pf FROM lw321 WHERE periode = '" . $tgl_max['0'] . "' AND tgl_realisasi = '" . $periode_date . "-$z' AND branch = '" . $dat_u['kode_uker'] . "'";
                    }
                    // echo $rz;
                    $rs_rz = mysqli_query($koneksi, $rz);
                    $dat_pf = mysqli_fetch_array($rs_rz);

                    if ($dat_pf['pf'] == '') {
                        $plaf_neraca = get_real_gi405($periode_, $dat_u['kode_uker']);
                        $plafond = ($plaf_neraca < 10000000) ? 0 : $plaf_neraca / $satuan;
                    } else {
                        $plafond = $dat_pf['pf'] / $satuan;
                    }

                    $total_data['plafond'][$z] += $plafond;
                    $x++;
                ?>
                    <td><?php echo number_format($plafond); ?></td>
                <?php } ?>
            </tr>
        <?php } ?>
    </tbody>

    <tfoot class="table-dark">
        <tr>
            <th></th>
            <th>TOTAL</th>
            <?php if ($_GET['view_unit'] == 'on'): ?>
                <th></th>
            <?php endif; ?>
            <th><?php echo number_format($total_data['jum_mantri']); ?></th>
            <th><?php echo number_format($total_data['rek_all']); ?></th>
            <th><?php echo number_format($total_data['plaf_all']); ?></th>
            <th><?php echo number_format($total_data['rek_baru']); ?></th>
            <th><?php echo number_format($total_data['real_baru']); ?></th>
            <th><?php echo number_format($total_data['rek_suplesi']); ?></th>
            <th><?php echo number_format($total_data['real_suplesi']); ?></th>
            <th><?php echo number_format($total_data['rek_0']); ?></th>
            <th><?php echo number_format($total_data['plaf_0']); ?></th>
            <th><?php echo number_format($total_data['rek_50']); ?></th>
            <th><?php echo number_format($total_data['plaf_50']); ?></th>
            <th><?php echo number_format($total_data['rek_100']); ?></th>
            <th><?php echo number_format($total_data['plaf_100']); ?></th>
            <th><?php echo number_format($total_data['rek_300']); ?></th>
            <th><?php echo number_format($total_data['plaf_300']); ?></th>
            <th><?php echo number_format($total_data['rek_300_nongbt']); ?></th>
            <th><?php echo number_format($total_data['plaf_300_nongbt']); ?></th>
            <th><?php echo number_format($total_data['plaf_all'] / $total_data['jum_mantri'] / $hariKerjaTerlalui, 2); ?></th>
            <th><?php echo number_format($total_data['guidance']); ?></th>
            <th><?php echo number_format($total_data['guidance'] - $total_data['plaf_all']); ?></th>
            <th><?php echo number_format(($total_data['plaf_all'] / $total_data['guidance']) * 100, 2); ?></th>
            <?php for ($i = 1; $i <= $hari; $i++): ?>
                <th><?php echo number_format($total_data['plafond'][$i]); ?></th>
            <?php endfor; ?>
        </tr>
    </tfoot>
</table>


<hr>