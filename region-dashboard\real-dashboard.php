<?php
session_start();

// print_r($_POST);
$auth_role = array('super354', 'god', 'area');
function sanitize_input($input)
{
    $input = trim($input);
    $input = stripslashes($input);
    $input = htmlspecialchars($input);
    return $input;
}

function getDates($month, $year)
{
    // Format tanggal awal
    $tanggal_awal = date("Y-m-d", strtotime("$year-$month-01"));

    // Format tanggal akhir (tanggal 1 bulan berikutnya)
    $tanggal_akhir = date("Y-m-d", strtotime("$tanggal_awal +1 month"));

    // Return sebagai array
    return [
        "tanggal_awal" => $tanggal_awal,
        "tanggal_akhir" => $tanggal_akhir
    ];
}



if (isset($_SESSION['role']) && !in_array($_SESSION['role'], $auth_role)) {
    // Kode yang akan dijalankan jika role tidak ada dalam array
    die('NOT authorized!!');
}

$view = htmlspecialchars($_GET['view_unit']);

if ($view == '') {
    if ($_POST['pilih-uker'] == '') {
        if ($_SESSION['role'] == 'god') {
            $uker = get_uker_region_kc($_SESSION['kodeuker']);
        } else if ($_POST['pilih-uker'] == '' && $_SESSION['role'] == 'area' && $_GET['pilih-uker'] == '') {
            $uker = get_uker_region_kc($_SESSION['kodeuker']);
            $unitview = "on";
        } else {
            $uker = get_list_unit_active($_SESSION['kodeuker']);
            $unitview = "on";
        }
    } else {
        $uker = get_list_unit_active($_POST['pilih-uker']);
        $uker2 = get_list_unit_active($_POST['pilih-uker']);
        $unitview = "on";
    }
    // $uker = get_list_unit_active_f();
    // $uker2 = get_list_unit_active_f();
} else if ($view == 'on') {
    $uker = get_list_unit_active_f();
}
//$satuan = 1000000;
$satuan = 1;
$month = $_POST['month'] ?? date('m');
$year = $_POST['year'] ?? date('Y');

$sql_max_real = 'select max(periode) from lw321';
$rs_max = mysqli_query($koneksi, $sql_max_real);
$tgl_max = mysqli_fetch_array($rs_max);

$liburNasional = array(
    '2025-01-01',
    '2025-01-27',
    '2025-01-29',
    '2025-03-29',
    '2025-03-31',
    '2025-04-01',
    '2025-04-18',
    '2025-04-20',
    '2025-05-01',
    '2025-05-12',
    '2025-05-29',
    '2025-06-01',
    '2025-06-06',
    '2025-06-27',
    '2025-06-09',
    '2025-08-17',
    '2025-09-05',
    '2025-12-25',
    '2025-01-28',
    '2025-03-28',
    '2025-04-02',
    '2025-04-03',
    '2025-04-04',
    '2025-04-07',
    '2025-05-13',
    '2025-05-30',
    '2025-12-26'
);

$tanggalMulai = date('d');  // Tanggal mulai

$hk = getHariKerjaRange(1, cal_days_in_month(CAL_GREGORIAN, $month, $year), $month, $year, $liburNasional);


$total_hari_kerja = $hk['total'];

// Hari kerja yang sudah dilalui (misalnya sampai tanggal 12 April)
$hariKerjaTerlalui = $hk['terlalui'];

// Sisa hari kerja
$sisaHariKerja = $hk['sisa'];

// echo $total_hari_kerja . " " . $hariKerjaTerlalui . " " . $sisaHariKerja;

//$total_hari_kerja = HariKerja($month, $year);

$tanggalHariIni = new DateTime();
$month_date_hari_ini = date_format($tanggalHariIni, 'Y-m');
if ($month_date_hari_ini == ($_POST['year'] . '-' . $_POST['month'])) {
    $tgl_hari_ini = 1;
}

if (($_POST['month'] == '' || $_POST['year'] == '') && ($tgl_hari_ini != '1')) {
    $periode_date = date('Y') . '-' . date('m');
    $month = date('m');
    $year = date('Y');
    $sisa_hari_kerja = hitungSisaHariKerja($tanggalMulai, $month, $year);
    $sudahHariKerja = $total_hari_kerja - $sisa_hari_kerja;
} else {
    $periode_date = $_POST['year'] . '-' . $_POST['month'];
    $month = $_POST['month'];
    $year = $_POST['year'];
    $tgl_max[0] = getTanggalAkhirBulan($month, $year);
    $datedprev = getTanggalAkhirBulanSebelumnya($year . '-' . $month . '-01');
    $sudahHariKerja = $total_hari_kerja;
};

?>
<h5 class="text-center">Kinerja Realisasi Pinjaman ( Harian ) </h5>

<?php
echo '
<div class="container d-flex justify-content-center mt-3">
    <div class="d-flex gap-2" style="max-width: 460px;">
        <div class="card shadow-sm border-0 rounded-3 text-center p-2 flex-fill" style="background-color: #e8f0fe;">
            <div class="text-muted small">
                <i class="bi bi-calendar-range me-1"></i>Total Hari Kerja
            </div>
            <div class="fw-semibold text-primary fs-6">' . $total_hari_kerja . '</div>
        </div>
        <div class="card shadow-sm border-0 rounded-3 text-center p-2 flex-fill" style="background-color: #e6f4ea;">
            <div class="text-muted small">
                <i class="bi bi-calendar-check me-1"></i>Sisa Hari Kerja
            </div>
            <div class="fw-semibold text-success fs-6">' . $sisaHariKerja . '</div>
        </div>
        <div class="card shadow-sm border-0 rounded-3 text-center p-2 flex-fill" style="background-color: #f0f4f8;">
            <div class="text-muted small">
                <i class="bi bi-calendar2-week me-1"></i>HK Telah Dilalui
            </div>
            <div class="fw-semibold text-info fs-6">' . $hariKerjaTerlalui . '</div>
        </div>
    </div>
</div>';

function getLastDayOfMonth($year, $month)
{
    return date('Y-m-t', strtotime("$year-$month-01"));
}

$tgl_akhir_bulan = getLastDayOfMonth($year, $month);

$hari = cal_days_in_month(CAL_GREGORIAN, $month, $year);
$arr_month = array('Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni', 'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember');


$z = 12;
$arr_i = array();
for ($i = 1; $i <= $hari; $i++) {
    $arr_i[$i] = $z;
    $z++;
}

$series_hari = implode(',', $arr_i);

$y = 3;
for ($i = 1; $i <= $hari; $i++) {
    $arr_z[$i] = $y;
    $y++;
}

$series_hari2 = implode(',', $arr_z);

$start_date = $_POST['month'];
$end_date = $tgl_max[0];

?>
<!-- jQuery -->
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>


<!-- DataTables Core CSS & JS -->
<link rel="stylesheet" href="https://cdn.datatables.net/2.2.2/css/dataTables.dataTables.min.css">

<script src="https://cdn.datatables.net/2.2.2/js/dataTables.min.js"></script>
<script src="https://cdn.datatables.net/plug-ins/2.0.0/dataRender/number.js"></script>
<script src="https://cdn.datatables.net/plug-ins/2.0.0/dataRender/ellipsis.js"></script>


<!-- Buttons Plugin CSS & JS -->
<link rel="stylesheet" href="https://cdn.datatables.net/buttons/3.1.0/css/buttons.dataTables.min.css">
<script src="https://cdn.datatables.net/buttons/3.1.0/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/buttons/3.1.0/js/buttons.html5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/3.1.0/js/buttons.print.min.js"></script>


<!-- JSZip for Excel Export -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.2.7/pdfmake.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.2.7/vfs_fonts.js"></script>

<!-- Bootstrap 5 CSS -->
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
<!-- Bootstrap 5 Bundle JS (includes Popper) -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

<!-- Font Awesome for Icons -->
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

<!-- Custom CSS for Weekend Styling -->
<style>
    /* Enhanced weekend styling */
    .weekend-header {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%) !important;
        box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);
        position: relative;
    }

    .weekend-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: repeating-linear-gradient(45deg,
                transparent,
                transparent 2px,
                rgba(255, 255, 255, 0.1) 2px,
                rgba(255, 255, 255, 0.1) 4px);
        pointer-events: none;
    }

    .weekend-cell {
        background: rgba(220, 53, 69, 0.15) !important;
        border-left: 3px solid #dc3545 !important;
        border-right: 3px solid #dc3545 !important;
        position: relative;
    }

    .weekend-cell::before {
        content: '🔴';
        position: absolute;
        top: 2px;
        right: 2px;
        font-size: 8px;
        opacity: 0.7;
    }

    .weekday-header {
        background: linear-gradient(135deg, #0d6efd 0%, #0b5ed7 100%) !important;
        box-shadow: 0 2px 4px rgba(13, 110, 253, 0.3);
    }

    /* Table enhancements */
    #tabel-data {
        border-collapse: separate;
        border-spacing: 0;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        border-radius: 8px;
        overflow: hidden;
    }

    #tabel-data th {
        border: 1px solid rgba(255, 255, 255, 0.2);
        font-weight: 600;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    }

    #tabel-data td {
        border: 1px solid #dee2e6;
        transition: all 0.2s ease;
    }

    #tabel-data tbody tr:hover td {
        background-color: rgba(13, 110, 253, 0.05);
    }

    /* Legend styling */
    .legend-container {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 15px;
        margin: 20px 0;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .legend-item {
        display: inline-flex;
        align-items: center;
        margin: 5px 15px 5px 0;
        padding: 5px 10px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 500;
    }

    .legend-weekend {
        background: rgba(220, 53, 69, 0.15);
        border: 1px solid #dc3545;
        color: #721c24;
    }

    .legend-weekday {
        background: rgba(13, 110, 253, 0.15);
        border: 1px solid #0d6efd;
        color: #084298;
    }

    /* National Holiday Styling */
    .holiday-header {
        background: linear-gradient(135deg, #6c757d 0%, #495057 100%) !important;
        box-shadow: 0 2px 4px rgba(108, 117, 125, 0.3);
        position: relative;
    }

    .holiday-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: repeating-linear-gradient(45deg,
                transparent,
                transparent 2px,
                rgba(255, 255, 255, 0.2) 2px,
                rgba(255, 255, 255, 0.2) 4px);
        pointer-events: none;
    }

    .holiday-cell {
        background: rgba(108, 117, 125, 0.2) !important;
        border-left: 3px solid #6c757d !important;
        border-right: 3px solid #6c757d !important;
        position: relative;
    }

    .holiday-cell::before {
        content: '🏛️';
        position: absolute;
        top: 2px;
        right: 2px;
        font-size: 8px;
        opacity: 0.8;
    }

    .legend-holiday {
        background: rgba(108, 117, 125, 0.2);
        border: 1px solid #6c757d;
        color: #495057;
    }

    /* Responsive improvements */
    @media (max-width: 768px) {
        .legend-container {
            text-align: center;
        }

        .legend-item {
            display: block;
            margin: 5px auto;
            text-align: center;
            max-width: 200px;
        }
    }
</style>







<script>
    $(document).ready(function() {
        new DataTable('#tabel-data', {
            paging: false,
            "autoWidth": true,
            searching: false,
            ordering: true,
            info: true,
            responsive: true,
            scrollX: true,
            scrollY: 800,
            scrollCollapse: true,
            fixedHeader: true,

            fixedColumns: {
                leftColumns: 1,

            },

            // Kalau pakai export buttons
            buttons: ['copy', 'excel'],
            dom: 'Bfrtip',

            // Optional: Sesuaikan default page length dan bahasa
            pageLength: 50,

            columnDefs: [{
                targets: [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, <?php echo $series_hari; ?>],
                className: "text-end"
            }],

        });
    });
</script>



<script>
    document.title = 'Summary Kinerja Realisasi Pinjaman Periode <?php echo tgl_periode($tgl_max); ?>'
</script>


<p class="text-center">
<div class="container-fluid">
    <form action="" method="post">
        <input type="hidden" name="pilih-area" value="<?php echo sanitize_input($_POST['pilih-area']); ?>">
        <input type="hidden" name="pilih-uker" value="<?php echo sanitize_input($_POST['pilih-uker']); ?>">
        <input type="hidden" name="view_unit" value="<?php echo sanitize_input($_GET['view_unit']); ?>">
        <input type="hidden" name="pilih_periode" value="<?php echo sanitize_input($_GET['pilih_periode']); ?>">
        <input type="hidden" name="month" value="<?php echo sanitize_input($_GET['month']); ?>">
        <input type="hidden" name="year" value="<?php echo sanitize_input($_GET['year']); ?>">
        <label>Periode Disburse : </label> <select name="month">
            <?php
            $i = 0;
            foreach ($arr_month as $bln) {
                $i++;
                if ($_POST['month'] == '') {
                    $_POST['month'] = date('m');
                }
                if ($i == $_POST['month']) {
                    $sl = 'selected';
                } else {
                    $sl = '';
                }
            ?>
                <option value="<?php echo $i; ?>" <?php echo $sl; ?>><?php echo $bln; ?></option>
            <?php } ?>
        </select>

        <select name="year">
            <?php
            if ($_POST['year'] == '') {
                $_POST['year'] = date('Y');
            }

            for ($y = ($year - 1); $y <= $year; $y++) {
                if ($y == $_POST['year']) {
                    $sl = 'selected';
                } else {
                    $sl = '';
                }
            ?>
                <option value="<?php echo $y ?>" <?php echo $sl ?>><?php echo $y; ?></option>
            <?php } ?>
        </select>
        <input type="button" value="Refresh Data" class="btn btn-dark" onclick="this.form.submit()">
    </form>
</div>
</p>
<?php

if (trim($_GET['view_unit']) == 'on' || $unitview == 'on') {
    $branch = 'branch';
    $where_main_branch = " and main_branch = '" . $_SESSION['kodeuker'] . "'";
} else {
    $branch = 'main_branch';
}



$dates_real = getDates($month, $year);

// plafond all
$sql_uker = "SELECT 
                    $branch, 
                    SUM(plafond) AS total,
                    COUNT(norek) AS rek,
                     SUM(CASE WHEN plafond > 300000000 AND type NOT IN (select type from type_pinjaman where kategori like 'Briguna') THEN plafond ELSE 0 END) AS plaf_300_nongbt,
                    COUNT(CASE WHEN plafond > 300000000 AND type NOT IN (select type from type_pinjaman where kategori like 'Briguna') THEN plafond ELSE 0 END) THEN 1 ELSE NULL END) AS rek_300_nongbt,
                    SUM(CASE WHEN plafond > 300000000 THEN plafond ELSE 0 END) AS plaf_300,
                    COUNT(CASE WHEN plafond > 300000000 THEN 1 ELSE NULL END) AS rek_300,
                     SUM(CASE WHEN plafond > 100000000 and plafond <= 300000000 THEN plafond ELSE 0 END) AS plaf_100,
                    COUNT(CASE WHEN plafond > 100000000 and plafond <= 300000000 THEN 1 ELSE NULL END) AS rek_100,
                    SUM(CASE WHEN plafond <= 50000000 THEN plafond ELSE 0 END) AS plaf_0,
                    COUNT(CASE WHEN plafond <= 50000000 THEN 1 ELSE NULL END) AS rek_0,
                    SUM(CASE WHEN plafond > 50000000 AND plafond <= 100000000 THEN plafond ELSE 0 END) AS plaf_50,
                    COUNT(CASE WHEN plafond > 50000000 AND plafond <= 100000000 THEN 1 ELSE NULL END) AS rek_50
                FROM 
                    lw321
                WHERE 
                    tgl_realisasi > '" . $dates_real['tanggal_awal'] . "'
                    and tgl_realisasi < '" . $dates_real['tanggal_akhir'] . "'
                    and
                    periode = '" . $tgl_max[0] . "'
                   
                GROUP BY 
                    $branch
                ";

//echo $sql_uker;

$plafond_data[] = "";
$uker_list = mysqli_query($koneksi, $sql_uker);
while ($row = mysqli_fetch_assoc($uker_list)) {
    $plafon_data[$row[$branch]] = $row;
}


$sql_suplesi = "
    SELECT 
        $branch,
        SUM(plafond) AS total_suplesi,
        COUNT(norek) AS rek_suplesi
    FROM 
        lw321
    WHERE 
        tgl_realisasi > '$datedprev'
        AND tgl_realisasi <= '$tgl_max[0]'
        AND periode = '$tgl_max[0]'
        AND cif IN (
            SELECT DISTINCT cif 
            FROM lw321 
            WHERE periode = '$datedprev'
        )
    GROUP BY 
        $branch
";

//echo $sql_suplesi;

// Eksekusi query
$result2 = mysqli_query($koneksi, $sql_suplesi);

// Simpan hasil ke array
$suplesi_data = [];
while ($row = mysqli_fetch_assoc($result2)) {
    $suplesi_data[$row[$branch]] = $row;
}

// echo '<pre>';
// print_r($suplesi_data);
// echo '</pre>';



$sql_baru = "
    SELECT 
        $branch,
        SUM(plafond) AS total_baru ,
        COUNT(norek) AS rek_baru
    FROM 
        lw321
    WHERE 
        tgl_realisasi > '$datedprev'
        AND tgl_realisasi <= '$tgl_max[0]'
        AND periode = '$tgl_max[0]'
        AND cif NOT IN (
            SELECT DISTINCT cif 
            FROM lw321 
            WHERE periode = '$datedprev'
        )
    GROUP BY 
        $branch
";

// Eksekusi query
$result1 = mysqli_query($koneksi, $sql_baru);

///echo $sql_baru;


// Simpan hasil ke array
$baru_data = [];
while ($row = mysqli_fetch_assoc($result1)) {
    $baru_data[$row[$branch]] = $row;
}

$sql_mbm_suplesi = "SELECT mu.id_mbm, m.nama_mbm, SUM(lw.plafond) AS total_suplesi,
                 COUNT(lw.norek) AS rek_suplesi
                  FROM lw321 lw 
                  JOIN mbm_uker mu 
                  ON lw.branch = mu.id_uker 
                  JOIN mbm m 
                  ON mu.id_mbm = m.pn_mbm 
                  WHERE lw.tgl_realisasi > '$datedprev'
                  AND lw.tgl_realisasi <= '$tgl_max[0]'
                   AND lw.periode = '$tgl_max[0]'
                    AND NOT EXISTS ( SELECT 1 FROM lw321 prev WHERE prev.cif = lw.cif AND prev.periode = '$datedprev' ) 
                    GROUP BY mu.id_mbm, m.nama_mbm;";


$sql_mbm_baru = "SELECT mu.id_mbm, m.nama_mbm, SUM(lw.plafond) AS total_suplesi,
                    COUNT(lw.norek) AS rek_suplesi
                FROM lw321 lw 
                JOIN mbm_uker mu 
                ON lw.branch = mu.id_uker 
                JOIN mbm m 
                ON mu.id_mbm = m.pn_mbm 
                WHERE lw.tgl_realisasi > '$datedprev'
                AND lw.tgl_realisasi <= '$tgl_max[0]'
                AND lw.periode = '$datedprev'
                AND NOT EXISTS ( SELECT 1 FROM lw321 prev WHERE prev.cif = lw.cif AND prev.periode = '$datedprev' ) 
                GROUP BY mu.id_mbm, m.nama_mbm";

include 'menu_real.php';

if ($_SESSION['role'] == 'god') { ?>
    | <a href="?mode=realisasi&view_unit=on" class="btn btn-dark">Unit View</a> | <a href="?mode=realmbm" class="btn btn-dark">MBM View</a>
<?php } ?>

<!-- Legend for Weekend/Weekday Colors -->
<div class="legend-container">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h6 class="mb-2"><i class="fas fa-info-circle me-2"></i>Keterangan Warna Hari:</h6>
            <div class="d-flex flex-wrap align-items-center">
                <div class="legend-item legend-weekday">
                    <span class="me-2">💼</span>
                    <span>Hari Kerja (Senin - Jumat)</span>
                </div>
                <div class="legend-item legend-weekend">
                    <span class="me-2">🔴</span>
                    <span>Weekend (Sabtu - Minggu)</span>
                </div>
                <div class="legend-item legend-holiday">
                    <span class="me-2">🏛️</span>
                    <span>Libur Nasional</span>
                </div>
            </div>
        </div>
        <div class="col-md-4 text-end">
            <small class="text-muted">
                <i class="fas fa-calendar-alt me-1"></i>
                Periode: <?php echo $arr_month[$month - 1] . ' ' . $year; ?>
            </small>
        </div>
    </div>
</div>

<table id="tabel-data" class="table-striped table-bordered table" style="width:100%" data-page-length='50' cellpadding="2" cellspacing="2">
    <thead class="table-dark">
        <tr>
            <th rowspan="2">BC</th>
            <th rowspan="2">Branch Office</th>
            <?php if ($_GET['view_unit'] == 'on'): ?>
                <th rowspan="2">Main Branch</th>
            <?php endif; ?>
            <th rowspan="2">Jum <br> Mantri</th>
            <th colspan="2" class="text-center">Total <br> Disburse</th>
            <th colspan="2" class="text-center">Baru</th>
            <th colspan="2" class="text-center">Suplesi</th>
            <th colspan="2" class="text-center">Plaf <br>
                0 - 50jt </th>
            <th colspan="2" class="text-center">Plaf <br>
                51 - 100jt </th>
            <th colspan="2" class="text-center">Plaf <br> 100 - 300jt </th>
            <th colspan="2" class="text-center">Plaf <br> > 300jt </th>
            <th rowspan="2">Ratas <br>Per Mantri</th>
            <th rowspan="2">Guidance</th>
            <th rowspan="2">GAP <BR> Guidance</th>
            <th rowspan="2"> % <BR> Guidance </th>
            <?php
            // Enhanced day names with better formatting
            $nama_hari = array('Minggu', 'Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu');
            $nama_hari_singkat = array('Min', 'Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab');

            for ($i = 1; $i <= $hari; $i++) {
                $date_real = $periode_date . '-' . sprintf("%02d", $i);
                $day_of_week = date('w', strtotime($date_real)); // 0 (Sunday) to 6 (Saturday)
                $day_name = $nama_hari[$day_of_week];
                $day_name_short = $nama_hari_singkat[$day_of_week];

                // Check if date is national holiday
                $is_national_holiday = in_array($date_real, $liburNasional);

                // Enhanced day type detection with priority: National Holiday > Weekend > Weekday
                if ($is_national_holiday) {
                    $header_class = 'holiday-header';
                    $text_color = 'text-white';
                    $day_icon = '🏛️'; // Government building for national holidays
                    $day_type = 'Libur Nasional';
                } elseif ($day_of_week == 0 || $day_of_week == 6) {
                    $header_class = 'weekend-header';
                    $text_color = 'text-white';
                    $day_icon = '🔴'; // Red circle for weekends
                    $day_type = 'Weekend';
                } else {
                    $header_class = 'weekday-header';
                    $text_color = 'text-white';
                    $day_icon = '💼'; // Briefcase for weekdays
                    $day_type = 'Hari Kerja';
                }

                // Format tanggal dengan leading zero
                $formatted_date = sprintf("%02d", $i);
            ?>
                <th class="<?php echo $header_class ?> <?php echo $text_color ?> fw-bold text-center" rowspan="2"
                    style="min-width: 85px; font-size: 11px; line-height: 1.2; position: relative;"
                    title="<?php echo $day_name . ', ' . $formatted_date . ' - ' . $day_type; ?>">
                    <div style="padding: 4px 2px;">
                        <div class="mb-1">
                            <span style="font-size: 12px;"><?php echo $day_icon; ?></span>
                        </div>
                        <div class="mt-1">
                            <span class="badge bg-light text-dark px-1" style="font-size: 9px;">
                                <?php echo $day_name_short . '<br>Tgl ' . $formatted_date; ?>
                            </span>
                        </div>
                        <?php if ($is_national_holiday): ?>
                            <div class="mt-1">
                                <span class="badge bg-warning text-dark px-1" style="font-size: 7px;">
                                    LIBUR
                                </span>
                            </div>
                        <?php endif; ?>
                    </div>
                </th>
            <?php } ?>
        </tr>
        <tr>
            <th>Deb</th>
            <th>Rp</th>
            <th>Deb</th>
            <th>Rp</th>
            <th>Deb</th>
            <th>Rp</th>
            <th>Deb</th>
            <th>Rp</th>
            <th>Deb</th>
            <th>Rp</th>
            <th>Deb</th>
            <th>Rp</th>
            <th>Deb</th>
            <th>Rp</th>

        </tr>

    </thead>

    <tbody>
        <?php
        while ($dat_u = mysqli_fetch_array($uker)) {
            $type = cek_branch2($dat_u['kode_uker']);
            if ($view == 'mbm') {
                $jum_mantri = get_jml_mantri_mbm($dat_u['kode_uker']);
            } else {
                $jum_mantri = get_jumlah_mantri($dat_u['kode_uker']);
            }
            $guidance = get_guidance($dat_u['kode_uker'], $date_rka, '1');
            $guidance['target'] = $guidance['target'] * 1000000;

            // Calculate branch condition
            if ($type == 'kc') {
                $br = "main_branch ='" . $dat_u['kode_uker'] . "' and branch <> '" . $dat_u['kode_uker'] . "'";
            } elseif ($type == 'un') {
                $br = "branch = '" . $dat_u['kode_uker'] . "'";
            } elseif ($type == 'area') {
                $br = "branch IN (select kode_uker from uker where id_area = '" . $dat_u['kode_uker'] . "')";
            }


            $ratas_mantri = ($plafon_data[$dat_u['kode_uker']]['total'] / $satuan) / $jum_mantri / $hariKerjaTerlalui;

            // Store totals
            $total_data['jum_mantri'] += $jum_mantri;
            $total_data['rek_all'] += $plafon_data[$dat_u['kode_uker']]['rek'];
            $total_data['plaf_all'] += $plafon_data[$dat_u['kode_uker']]['total'] / $satuan;


            $total_data['real_baru'] += $baru_data[$dat_u['kode_uker']]['total_baru'] / $satuan;
            $total_data['rek_baru'] += $baru_data[$dat_u['kode_uker']]['rek_baru'];


            $total_data['real_suplesi'] += $suplesi_data[$dat_u['kode_uker']]['total_suplesi'] / $satuan;
            $total_data['rek_suplesi'] += $suplesi_data[$dat_u['kode_uker']]['rek_suplesi'];

            $total_data['rek_100'] += $plafon_data[$dat_u['kode_uker']]['rek_100'];
            $total_data['plaf_100'] += $plafon_data[$dat_u['kode_uker']]['plaf_100'] / $satuan;

            $total_data['rek_300'] += $plafon_data[$dat_u['kode_uker']]['rek_300'];
            $total_data['plaf_300'] += $plafon_data[$dat_u['kode_uker']]['plaf_300'] / $satuan;

            $total_data['plaf_0'] += $plafon_data[$dat_u['kode_uker']]['plaf_0'] / $satuan;
            $total_data['rek_0'] += $plafon_data[$dat_u['kode_uker']]['rek_0'];

            $total_data['plaf_50'] += $plafon_data[$dat_u['kode_uker']]['plaf_50'] / $satuan;
            $total_data['rek_50'] += $plafon_data[$dat_u['kode_uker']]['rek_50'];

            $total_data['guidance'] += $guidance['target'];
        ?>
            <tr>
                <td><?php echo htmlspecialchars($dat_u['kode_uker']); ?></td>
                <td><?php echo htmlspecialchars($dat_u['nama_uker']); ?></td>
                <?php if ($_GET['view_unit'] == 'on'): ?>
                    <td><?php
                        $main_b = get_main_branch($dat_u['kode_uker']);
                        echo htmlspecialchars(get_nama_uker($main_b, $koneksi));
                        ?></td>
                <?php endif; ?>
                <td><?php echo number_format($jum_mantri); ?></td>
                <td><?php echo $plafon_data[$dat_u['kode_uker']]['rek']; ?></td>
                <td><?php echo number_format($plafon_data[$dat_u['kode_uker']]['total'] / $satuan); ?></td>
                <td><?php echo $baru_data[$dat_u['kode_uker']]['rek_baru'] ?></td>
                <td><?php echo number_format($baru_data[$dat_u['kode_uker']]['total_baru'] / $satuan); ?></td>
                <td><?php echo $suplesi_data[$dat_u['kode_uker']]['rek_suplesi']; ?></td>
                <td><?php echo number_format($suplesi_data[$dat_u['kode_uker']]['total_suplesi'] / $satuan); ?></td>
                <td><?php echo $plafon_data[$dat_u['kode_uker']]['rek_0']; ?></td>
                <td><?php echo number_format($plafon_data[$dat_u['kode_uker']]['plaf_0'] / $satuan); ?></td>
                <td><?php echo $plafon_data[$dat_u['kode_uker']]['rek_50']; ?></td>
                <td><?php echo number_format($plafon_data[$dat_u['kode_uker']]['plaf_50'] / $satuan); ?></td>
                <td><?php echo $plafon_data[$dat_u['kode_uker']]['rek_100']; ?></td>
                <td><?php echo number_format($plafon_data[$dat_u['kode_uker']]['plaf_100'] / $satuan); ?></td>
                <td><?php echo $plafon_data[$dat_u['kode_uker']]['rek_300']; ?></td>
                <td><?php echo number_format($plafon_data[$dat_u['kode_uker']]['plaf_300'] / $satuan); ?></td>

                <td><?php echo number_format($ratas_mantri, 2); ?></td>
                <td><?php echo number_format($guidance['target']) ?></td>
                <td><?php echo number_format(($plafon_data[$dat_u['kode_uker']]['total'] / $satuan) - $guidance['target']); ?></td>
                <td><?php echo number_format((($plafon_data[$dat_u['kode_uker']]['total'] / $satuan) / $guidance['target']) * 100, 2) ?></td>
                <?php
                for ($z = 1; $z <= $hari; $z++) {
                    $periode_ = $periode_date . '-' . sprintf("%02d", $z);

                    // Determine styling for data cells with priority: National Holiday > Weekend > Weekday
                    $date_check = $periode_date . '-' . sprintf("%02d", $z);
                    $day_of_week_data = date('w', strtotime($date_check));
                    $is_holiday_data = in_array($date_check, $liburNasional);

                    // Cell styling with priority
                    if ($is_holiday_data) {
                        $cell_class = 'holiday-cell';
                        $cell_style = '';
                    } elseif ($day_of_week_data == 0 || $day_of_week_data == 6) {
                        $cell_class = 'weekend-cell';
                        $cell_style = '';
                    } else {
                        $cell_class = '';
                        $cell_style = '';
                    }

                    if ($_GET['view_unit'] == '') {
                        $rz = "SELECT SUM(plafond) AS pf FROM lw321 WHERE periode = '" . $tgl_max['0'] . "' AND tgl_realisasi = '" . $periode_date . "-$z' AND main_branch = '" . $dat_u['kode_uker'] . "' AND branch IN (SELECT kode_uker FROM uker WHERE region = 'F' AND uker_type = 'un')";
                    } else {
                        $rz = "SELECT SUM(plafond) AS pf FROM lw321 WHERE periode = '" . $tgl_max['0'] . "' AND tgl_realisasi = '" . $periode_date . "-$z' AND branch = '" . $dat_u['kode_uker'] . "'";
                    }
                    // echo $rz;
                    $rs_rz = mysqli_query($koneksi, $rz);
                    $dat_pf = mysqli_fetch_array($rs_rz);

                    if ($dat_pf['pf'] == '') {
                        $plaf_neraca = get_real_gi405($periode_, $dat_u['kode_uker']);
                        $plafond = ($plaf_neraca < 10000000) ? 0 : $plaf_neraca / $satuan;
                    } else {
                        $plafond = $dat_pf['pf'] / $satuan;
                    }

                    $total_data['plafond'][$z] += $plafond;
                    $x++;
                ?>
                    <td class="<?php echo $cell_class; ?> text-end" style="<?php echo $cell_style; ?>">
                        <?php echo number_format($plafond); ?>

                    </td>
                <?php } ?>
            </tr>
        <?php } ?>
    </tbody>

    <tfoot class="table-dark">
        <tr>
            <th></th>
            <th>TOTAL</th>
            <?php if ($_GET['view_unit'] == 'on'): ?>
                <th></th>
            <?php endif; ?>
            <th><?php echo number_format($total_data['jum_mantri']); ?></th>
            <th><?php echo number_format($total_data['rek_all']); ?></th>
            <th><?php echo number_format($total_data['plaf_all']); ?></th>
            <th><?php echo number_format($total_data['rek_baru']); ?></th>
            <th><?php echo number_format($total_data['real_baru']); ?></th>
            <th><?php echo number_format($total_data['rek_suplesi']); ?></th>
            <th><?php echo number_format($total_data['real_suplesi']); ?></th>
            <th><?php echo number_format($total_data['rek_0']); ?></th>
            <th><?php echo number_format($total_data['plaf_0']); ?></th>
            <th><?php echo number_format($total_data['rek_50']); ?></th>
            <th><?php echo number_format($total_data['plaf_50']); ?></th>
            <th><?php echo number_format($total_data['rek_100']); ?></th>
            <th><?php echo number_format($total_data['plaf_100']); ?></th>
            <th><?php echo number_format($total_data['rek_300']); ?></th>
            <th><?php echo number_format($total_data['plaf_300']); ?></th>

            <th><?php echo number_format($total_data['plaf_all'] / $total_data['jum_mantri'] / $hariKerjaTerlalui, 2); ?></th>
            <th><?php echo number_format($total_data['guidance']); ?></th>
            <th><?php echo number_format($total_data['guidance'] - $total_data['plaf_all']); ?></th>
            <th><?php echo number_format(($total_data['plaf_all'] / $total_data['guidance']) * 100, 2); ?></th>
            <?php for ($i = 1; $i <= $hari; $i++):
                // Styling for footer with priority: National Holiday > Weekend > Weekday
                $date_footer = $periode_date . '-' . sprintf("%02d", $i);
                $day_of_week_footer = date('w', strtotime($date_footer));
                $is_holiday_footer = in_array($date_footer, $liburNasional);

                if ($is_holiday_footer) {
                    $footer_class = 'bg-secondary text-white';
                    $footer_style = 'border: 2px solid #fff; position: relative;';
                } elseif ($day_of_week_footer == 0 || $day_of_week_footer == 6) {
                    $footer_class = 'bg-danger text-white';
                    $footer_style = 'border: 2px solid #fff;';
                } else {
                    $footer_class = '';
                    $footer_style = '';
                }
            ?>
                <th class="<?php echo $footer_class; ?>" style="<?php echo $footer_style; ?>">
                    <?php if ($is_holiday_footer): ?>
                        <span style="position: absolute; top: 2px; right: 2px; font-size: 8px;">🏛️</span>
                    <?php endif; ?>
                    <?php echo number_format($total_data['plafond'][$i]); ?>
                </th>
            <?php endfor; ?>
        </tr>
    </tfoot>
</table>


<hr>