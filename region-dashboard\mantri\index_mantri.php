<link rel="stylesheet" href="https://cdn.datatables.net/fixedcolumns/5.0.4/css/fixedColumns.dataTables.css">
<link rel="stylesheet" href="https://cdn.datatables.net/2.2.2/css/dataTables.dataTables.min.css">

<script src="https://cdn.datatables.net/2.2.2/js/dataTables.min.js"></script>
<link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.3.2/css/buttons.dataTables.min.css">

<!-- Custom CSS untuk button export -->
<style>
    .custom-export-all {
        background-color: #28a745 !important;
        border-color: #28a745 !important;
        color: white !important;
        margin-right: 5px;
    }

    .custom-export-all:hover {
        background-color: #218838 !important;
        border-color: #1e7e34 !important;
    }

    .custom-export-page {
        background-color: #17a2b8 !important;
        border-color: #17a2b8 !important;
        color: white !important;
        margin-right: 5px;
    }

    .custom-export-page:hover {
        background-color: #138496 !important;
        border-color: #117a8b !important;
    }

    .custom-copy {
        background-color: #6c757d !important;
        border-color: #6c757d !important;
        color: white !important;
    }

    .custom-copy:hover {
        background-color: #5a6268 !important;
        border-color: #545b62 !important;
    }

    .dt-buttons {
        margin-bottom: 10px;
    }

    .dt-button {
        margin-right: 5px !important;
        padding: 6px 12px !important;
        font-size: 14px !important;
        border-radius: 4px !important;
    }
</style>

<script src="https://cdn.datatables.net/buttons/2.3.2/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.3.2/js/buttons.html5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.3.2/js/buttons.print.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.3.2/js/buttons.colVis.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
<script src="https://cdn.datatables.net/fixedcolumns/5.0.4/js/dataTables.fixedColumns.js"></script>

<?php

require_once 'mantri.class.php';
require_once 'region-function.php';

ini_set('max_execution_time', 0);

//print_r($_POST);
if (trim($_POST['pilih_periode_rank'] != '')) {
    $pilih_periode = $_POST['pilih_periode_rank'];
} else {
    $pilih_periode = $tgl_max;
}

//echo $pilih_periode;

if (trim($_POST['pilih_area']) == '' && trim($_POST['pilih-uker']) == '') {
    $pilih_uker = $_SESSION['kodeuker'];
} else if (trim($_POST['pilih-uker']) != '' && trim($_POST['pilih-unit']) == '') {
    $pilih_uker = htmlspecialchars($_POST['pilih-uker']);
} else if (trim($_POST['pilih-uker']) != '' && trim($_POST['pilih-unit']) != '') {
    $pilih_uker = htmlspecialchars($_POST['pilih-unit']);
} elseif (trim($_POST['pilih-area']) != '') {
    $pilih_uker = $_POST['pilih-area'];
}

$pilih_periode = trim(htmlspecialchars($pilih_periode));

// Tidak perlu mengambil data di sini karena akan diambil via AJAX
// $m = new Mantri($pilih_uker, $koneksi);
// $m->setPeriode($pilih_periode);
// $dat_m = $m->get_list_mantri();


?>

Test
<form action="" method="POST">
    <b>Pilih Periode:</b>

    <input type="hidden" name="pilih-uker" value="<?php echo $pilih_uker; ?>">
    <select name="pilih_periode_rank" onchange="this.form.submit()">
        <option value="">-- Pilih Periode --</option>
        <?php
        $nama_bulan = [
            1 => 'Januari',
            2 => 'Februari',
            3 => 'Maret',
            4 => 'April',
            5 => 'Mei',
            6 => 'Juni',
            7 => 'Juli',
            8 => 'Agustus',
            9 => 'September',
            10 => 'Oktober',
            11 => 'November',
            12 => 'Desember'
        ];

        $tahun = (int) date('Y');
        $bulan_aktif = (int) date('n');
        $hari_ini = date('Y-m-d'); // Hari ini
        // Pastikan $tgl_max diset dan valid sebelumnya
        // Contoh: $tgl_max = '2025-05-24';

        for ($bulan = 1; $bulan <= $bulan_aktif; $bulan++) {
            $tanggal_akhir = date("Y-m-t", strtotime("$tahun-$bulan-01"));
            $nama_bln = $nama_bulan[$bulan] ?? "Bulan-$bulan";

            if ($bulan == $bulan_aktif && $tanggal_akhir > $hari_ini) {
                // Bulan aktif dan belum akhir bulan → tampilkan $tgl_max
                $tgl_label = date("d", strtotime($tgl_max));
                $value_option = $tgl_max;
            } else {
                // Bulan sebelumnya atau bulan aktif yang sudah lewat
                $tgl_label = date("d", strtotime($tanggal_akhir));
                $value_option = $tanggal_akhir;
            }

            $selected = (isset($pilih_periode) && $value_option == $pilih_periode) ? 'selected' : '';

            echo '<option value="' . htmlspecialchars($value_option) . "\" $selected>" .
                "$tgl_label $nama_bln $tahun</option>";
        }
        ?>
    </select>

</form>
<?php

if (trim(htmlspecialchars($_POST['pilih_periode_rank'])) != '') {

?>



    <!-- Loading indicator -->
    <div id="loading-indicator" style="display:none; text-align:center; margin:20px 0;">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
        <p>Sedang memuat data mantri...</p>
    </div>




    <table id="tabel-data" class="table table-striped table-bordered compact hover" style="width:100%">
        <thead class="table-dark">
            <tr>
                <th>Pers N</th>
                <th>Nama Mantri</th>
                <th>JG</th>
                <th>Branch Office</th>
                <th>Unit</th>
                <th>Rp Realisasi</th>
                <th>Target Real</th>
                <th>Penc. Real (%)</th>
                <th>Rp OS</th>
                <th>Deb (%)</th>
                <th>%DPK</th>
                <th>%NPL</th>
                <th>%LAR</th>
                <th>Target %LAR</th>
                <th>Penc. %LAR</th>
                <th>Recovery <br>DH</th>
                <th>Target Rec DH</th>
                <th>Penc. DH</th>
                <th>QRIS <br>Berkualitas</th>
                <th>Target QRIS <br>Berkualitas</th>
                <th>Penc. QRIS <br>Berkualitas</th>
            </tr>
        </thead>
        <tbody>
            <!-- Data akan dimuat via AJAX -->
        </tbody>
        <tfoot>
            <tr>
                <th></th>
                <th></th>
                <th></th>
                <th></th>
                <th></th>
                <th></th>
                <th></th>
                <th></th>
                <th></th>
                <th></th>
                <th></th>
                <th></th>
                <th></th>
                <th></th>
                <th></th>
                <th></th>
                <th></th>
                <th></th>
                <th></th>
                <th></th>
                <th></th>


            </tr>
        </tfoot>
    </table>

    <script>
        $(document).ready(function() {

            // Ambil nilai periode dan uker dari form
            var pilih_periode = '<?php echo $pilih_periode; ?>';
            var pilih_uker = '<?php echo $pilih_uker; ?>';

            // Tampilkan loading indicator
            $('#loading-indicator').show();

            let table = $('#tabel-data').DataTable({
                // Aktifkan server-side processing
                processing: true,
                serverSide: true,
                ajax: {
                    url: 'mantri/data_mantri_serverside.php',
                    type: 'POST',
                    data: function(d) {
                        // Tambahkan parameter custom
                        d.pilih_periode = '<?php echo $pilih_periode; ?>';
                        d.pilih_uker = '<?php echo $pilih_uker; ?>';
                    },
                    error: function(xhr, error, thrown) {
                        console.error('Error loading data:', error);
                        console.error('Response Text:', xhr.responseText);
                        console.error('Status:', xhr.status);

                        // Hide loading indicator
                        $('#loading-indicator').hide();

                        // Show detailed error message
                        var errorMsg = 'Terjadi kesalahan saat memuat data mantri.';
                        if (xhr.status === 404) {
                            errorMsg += ' File tidak ditemukan.';
                        } else if (xhr.status === 500) {
                            errorMsg += ' Error server internal.';
                        } else if (xhr.status === 0) {
                            errorMsg += ' Tidak dapat terhubung ke server.';
                        }

                        // Display error in table
                        $('#tabel-data tbody').html(
                            '<tr><td colspan="21" class="text-center text-danger">' +
                            '<i class="fas fa-exclamation-triangle"></i> ' + errorMsg +
                            '<br><small>Silakan refresh halaman atau hubungi administrator.</small>' +
                            '</td></tr>'
                        );

                        // Optional: Show alert for critical errors
                        if (xhr.status === 500 || xhr.status === 0) {
                            alert(errorMsg + ' Silakan refresh halaman.');
                        }
                    }
                },

                order: [
                    [1, 'asc']
                ],
                scrollX: true,
                scrollY: "700px",
                scrollCollapse: true,

                paging: true,
                pageLength: 25,
                lengthMenu: [
                    [10, 25, 50, 100],
                    [10, 25, 50, 100]
                ],

                autoWidth: false,
                dom: '<"top"lBf>rt<"bottom"ip><"clear">',
                buttons: [{
                        text: '<i class="fas fa-file-excel text-warning"></i> Export All Excel',
                        className: 'btn btn-success custom-export-all',
                        action: function(e, dt, button, config) {
                            // Ambil elemen tombol yang diklik
                            var $btn = $(e.currentTarget);

                            // Simpan isi asli tombol
                            var originalHtml = $btn.html();

                            // Ubah isi tombol jadi loading spinner
                            $btn.html('<i class="fas fa-spinner fa-spin"></i> Exporting...').prop('disabled', true);

                            // Ambil nilai pencarian
                            var searchValue = dt.search();

                            // Gunakan variabel PHP yang sudah disiapkan sebagai JS (lihat catatan di bawah)
                            var exportUrl = 'mantri/export_mantri_excel.php?' +
                                'periode=' + pilih_periode +
                                '&uker=' + pilih_uker +
                                '&search=' + encodeURIComponent(searchValue);

                            // Buka export di tab baru
                            window.open(exportUrl, '_blank');

                            // Kembalikan isi tombol setelah delay
                            setTimeout(function() {
                                $btn.html(originalHtml).prop('disabled', false);
                            }, 2000);
                        }
                    },
                    {
                        extend: 'excelHtml5',
                        text: '<i class="fas fa-file-excel text-info"></i> Export Current Page',
                        title: 'Data Mantri (Halaman Saat Ini)',
                        className: 'btn btn-info custom-export-page',
                        footer: true,
                        exportOptions: {
                            modifier: {
                                page: 'current'
                            }
                        }
                    },
                    {
                        extend: 'copyHtml5',
                        text: '<i class="far fa-copy text-dark"></i> Copy Current Page',
                        className: 'btn btn-secondary custom-copy',
                        footer: true,
                        exportOptions: {
                            modifier: {
                                page: 'current'
                            }
                        }
                    }
                ],

                // Konfigurasi bahasa
                language: {
                    processing: "Sedang memproses...",
                    search: "Cari:",
                    lengthMenu: "Tampilkan _MENU_ data",
                    info: "Menampilkan _START_ sampai _END_ dari _TOTAL_ data",
                    infoEmpty: "Menampilkan 0 sampai 0 dari 0 data",
                    infoFiltered: "(disaring dari _MAX_ data keseluruhan)",
                    paginate: {
                        first: "Pertama",
                        previous: "Sebelumnya",
                        next: "Selanjutnya",
                        last: "Terakhir"
                    },
                    emptyTable: "Tidak ada data yang tersedia",
                    zeroRecords: "Tidak ditemukan data yang sesuai"
                },
                columns: [{
                        data: "pn",
                        title: "PN",
                        className: "text-center"
                    },
                    {
                        data: "nama",
                        title: "Nama Mantri",
                        className: "text-start"
                    },
                    {
                        data: "jg",
                        title: "JG",
                        className: "text-center"
                    },
                    {
                        data: "bo",
                        title: "Branch Office",
                        className: "text-start"
                    },
                    {
                        data: "unit",
                        title: "Unit",
                        className: "text-start"
                    },
                    {
                        data: "os",
                        title: "Rp Realisasi",
                        className: "text-end",
                        defaultContent: '0',
                        render: function(data, type, row) {
                            if (type === 'display' || type === 'type') {
                                var num = parseFloat(data) || 0;
                                return num.toLocaleString('en-US', {
                                    minimumFractionDigits: 0,
                                    maximumFractionDigits: 0
                                });
                            }
                            return data;
                        }
                    },
                    {
                        data: "target_os",
                        title: "Target Real",
                        className: "text-end",
                        defaultContent: '0',
                        render: function(data, type, row) {
                            if (type === 'display' || type === 'type') {
                                var num = parseFloat(data) || 0;
                                return num.toLocaleString('en-US', {
                                    minimumFractionDigits: 0,
                                    maximumFractionDigits: 0
                                });
                            }
                            return data;
                        }
                    },
                    {
                        data: "persen_os",
                        title: "Penc. Real (%)",
                        className: "text-end",
                        defaultContent: '0.00',
                        render: function(data, type, row) {
                            if (type === 'display' || type === 'type') {
                                var num = parseFloat(data) || 0;
                                return num.toLocaleString('en-US', {
                                    minimumFractionDigits: 2,
                                    maximumFractionDigits: 2
                                }) + '%';
                            }
                            return data;
                        }
                    },
                    {
                        data: "rpos",
                        title: "Rp OS",
                        className: "text-end",
                        defaultContent: '0',
                        render: function(data, type, row) {
                            if (type === 'display' || type === 'type') {
                                var num = parseFloat(data) || 0;
                                return num.toLocaleString('en-US', {
                                    minimumFractionDigits: 0,
                                    maximumFractionDigits: 0
                                });
                            }
                            return data;
                        }
                    },
                    {
                        data: "debos",
                        title: "Deb",
                        className: "text-end",
                        defaultContent: '0',
                        render: function(data, type, row) {
                            if (type === 'display' || type === 'type') {
                                var num = parseFloat(data) || 0;
                                return num.toLocaleString('en-US', {
                                    minimumFractionDigits: 0,
                                    maximumFractionDigits: 0
                                });
                            }
                            return data;
                        }
                    },
                    {
                        data: "%dpk",
                        title: "%DPK",
                        className: "text-end",
                        defaultContent: '0.00',
                        render: function(data, type, row) {
                            if (type === 'display' || type === 'type') {
                                var num = parseFloat(data) || 0;
                                return num.toLocaleString('en-US', {
                                    minimumFractionDigits: 2,
                                    maximumFractionDigits: 2
                                }) + '%';
                            }
                            return data;
                        }
                    },
                    {
                        data: "%npl",
                        title: "%NPL",
                        className: "text-end",
                        defaultContent: '0.00',
                        render: function(data, type, row) {
                            if (type === 'display' || type === 'type') {
                                var num = parseFloat(data) || 0;
                                return num.toLocaleString('en-US', {
                                    minimumFractionDigits: 2,
                                    maximumFractionDigits: 2
                                }) + '%';
                            }
                            return data;
                        }
                    },
                    {
                        data: "%lar",
                        title: "%LAR",
                        className: "text-end",
                        defaultContent: '0.00',
                        render: function(data, type, row) {
                            if (type === 'display' || type === 'type') {
                                var num = parseFloat(data) || 0;
                                return num.toLocaleString('en-US', {
                                    minimumFractionDigits: 2,
                                    maximumFractionDigits: 2
                                }) + '%';
                            }
                            return data;
                        }
                    },
                    {
                        data: "target_lar",
                        title: "Target %LAR",
                        className: "text-end",
                        defaultContent: '0.00',
                        render: function(data, type, row) {
                            if (type === 'display' || type === 'type') {
                                var num = parseFloat(data) || 0;
                                return num.toLocaleString('en-US', {
                                    minimumFractionDigits: 2,
                                    maximumFractionDigits: 2
                                }) + '%';
                            }
                            return data;
                        }
                    },
                    {
                        data: "pencapaian_lar",
                        title: "Penc. %LAR",
                        className: "text-end",
                        defaultContent: '0.00',
                        render: function(data, type, row) {
                            if (type === 'display' || type === 'type') {
                                var num = parseFloat(data) || 0;
                                return num.toLocaleString('en-US', {
                                    minimumFractionDigits: 2,
                                    maximumFractionDigits: 2
                                }) + '%';
                            }
                            return data;
                        }
                    },
                    {
                        data: "recdh",
                        title: "Recovery DH",
                        className: "text-end",
                        defaultContent: '0',
                        render: function(data, type, row) {
                            if (type === 'display' || type === 'type') {
                                var num = parseFloat(data) || 0;
                                return num.toLocaleString('en-US', {
                                    minimumFractionDigits: 0,
                                    maximumFractionDigits: 0
                                });
                            }
                            return data;
                        }
                    },
                    {
                        data: "target_recdh",
                        title: "Target Rec DH",
                        className: "text-end",
                        defaultContent: '0',
                        render: function(data, type, row) {
                            if (type === 'display' || type === 'type') {
                                var num = parseFloat(data) || 0;
                                return num.toLocaleString('en-US', {
                                    minimumFractionDigits: 0,
                                    maximumFractionDigits: 0
                                });
                            }
                            return data;
                        }
                    },
                    {
                        data: "persen_recdh",
                        title: "Penc. DH (%)",
                        className: "text-end",
                        defaultContent: '0.00',
                        render: function(data, type, row) {
                            if (type === 'display' || type === 'type') {
                                var num = parseFloat(data) || 0;
                                return num.toLocaleString('en-US', {
                                    minimumFractionDigits: 2,
                                    maximumFractionDigits: 2
                                }) + '%';
                            }
                            return data;
                        }
                    },
                    {
                        data: "qris",
                        title: "QRIS Berkualitas",
                        className: "text-end",
                        defaultContent: '0',
                        render: function(data, type, row) {
                            if (type === 'display' || type === 'type') {
                                var num = parseFloat(data) || 0;
                                return num.toLocaleString('en-US', {
                                    minimumFractionDigits: 0,
                                    maximumFractionDigits: 0
                                });
                            }
                            return data;
                        }
                    },
                    {
                        data: "rka_qris",
                        title: "Target QRIS",
                        className: "text-end",
                        defaultContent: '0',
                        render: function(data, type, row) {
                            if (type === 'display' || type === 'type') {
                                var num = parseFloat(data) || 0;
                                return num.toLocaleString('en-US', {
                                    minimumFractionDigits: 0,
                                    maximumFractionDigits: 0
                                });
                            }
                            return data;
                        }
                    },
                    {
                        data: "persen_qris",
                        title: "Penc. QRIS (%)",
                        className: "text-end",
                        defaultContent: '0.00',
                        render: function(data, type, row) {
                            if (type === 'display' || type === 'type') {
                                var num = parseFloat(data) || 0;
                                return num.toLocaleString('en-US', {
                                    minimumFractionDigits: 2,
                                    maximumFractionDigits: 2
                                }) + '%';
                            }
                            return data;
                        }
                    }
                ],

                // Event handler untuk menyembunyikan loading indicator
                initComplete: function() {
                    $('#loading-indicator').hide();
                },

                drawCallback: function() {
                    $('#loading-indicator').hide();
                }
            });

            // Event handler untuk refresh data saat periode berubah
            $('select[name="pilih_periode_rank"]').on('change', function() {
                $('#loading-indicator').show();
                table.ajax.reload(function() {
                    $('#loading-indicator').hide();
                });
            });
        });
    </script>

<?php } ?>