<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Data Management</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .dashboard-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin: 30px 0;
            text-align: center;
        }

        .dashboard-title {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .dashboard-subtitle {
            color: #6c757d;
            font-size: 1.1rem;
        }

        .stats-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            padding: 25px;
            margin: 15px 0;
            text-align: center;
            transition: all 0.3s ease;
        }

        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }

        .stats-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            font-size: 1.5rem;
            color: white;
        }

        .stats-number {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .stats-label {
            color: #6c757d;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .primary-gradient { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .success-gradient { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
        .warning-gradient { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }
        .danger-gradient { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }

        .text-primary-gradient { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .text-success-gradient { 
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .text-warning-gradient { 
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .text-danger-gradient { 
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .recent-activity {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            padding: 25px;
            margin: 20px 0;
        }

        .activity-item {
            padding: 15px;
            border-left: 3px solid #667eea;
            margin-bottom: 15px;
            background: #f8f9fa;
            border-radius: 0 8px 8px 0;
        }

        .activity-time {
            font-size: 0.8rem;
            color: #6c757d;
        }

        .activity-text {
            margin: 5px 0 0 0;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- Dashboard Header -->
        <div class="dashboard-header">
            <h1 class="dashboard-title">
                <i class="fas fa-tachometer-alt"></i> Dashboard
            </h1>
            <p class="dashboard-subtitle">
                Monitoring dan Management Data NPL & DPK
            </p>
        </div>

        <div class="row">
            <!-- Left Column - Stats -->
            <div class="col-lg-8">
                <!-- Statistics Cards -->
                <div class="row">
                    <div class="col-md-3">
                        <div class="stats-card">
                            <div class="stats-icon primary-gradient">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="stats-number text-primary-gradient">1,234</div>
                            <div class="stats-label">Total NPL Records</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <div class="stats-icon success-gradient">
                                <i class="fas fa-coins"></i>
                            </div>
                            <div class="stats-number text-success-gradient">5,678</div>
                            <div class="stats-label">DPK Records</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <div class="stats-icon warning-gradient">
                                <i class="fas fa-target"></i>
                            </div>
                            <div class="stats-number text-warning-gradient">89%</div>
                            <div class="stats-label">Target Achievement</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <div class="stats-icon danger-gradient">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="stats-number text-danger-gradient">12</div>
                            <div class="stats-label">Pending Updates</div>
                        </div>
                    </div>
                </div>

                <!-- Data Management Menu Component -->
                <?php include 'menu_component.php'; ?>

                <!-- Recent Activity -->
                <div class="recent-activity">
                    <h5><i class="fas fa-history"></i> Recent Activity</h5>
                    <div class="activity-item">
                        <div class="activity-time">2 hours ago</div>
                        <div class="activity-text">New NPL data generated for December 2024</div>
                    </div>
                    <div class="activity-item">
                        <div class="activity-time">5 hours ago</div>
                        <div class="activity-text">Target Level DPK3 updated for Region A</div>
                    </div>
                    <div class="activity-item">
                        <div class="activity-time">1 day ago</div>
                        <div class="activity-text">Cache data refreshed successfully</div>
                    </div>
                    <div class="activity-item">
                        <div class="activity-time">2 days ago</div>
                        <div class="activity-text">New DPK generation completed</div>
                    </div>
                </div>
            </div>

            <!-- Right Column - Quick Actions & Info -->
            <div class="col-lg-4">
                <!-- Quick Actions -->
                <div class="recent-activity">
                    <h5><i class="fas fa-bolt"></i> Quick Actions</h5>
                    <div class="d-grid gap-2">
                        <button class="btn btn-outline-primary" onclick="window.open('view_cache_data.php', '_blank')">
                            <i class="fas fa-eye"></i> View Cache Data
                        </button>
                        <button class="btn btn-outline-success" onclick="window.open('debug_progress.php', '_blank')">
                            <i class="fas fa-bug"></i> System Diagnostic
                        </button>
                        <button class="btn btn-outline-info" onclick="refreshDashboard()">
                            <i class="fas fa-sync"></i> Refresh Dashboard
                        </button>
                        <button class="btn btn-outline-warning" onclick="showSystemStatus()">
                            <i class="fas fa-server"></i> System Status
                        </button>
                    </div>
                </div>

                <!-- System Status -->
                <div class="recent-activity">
                    <h5><i class="fas fa-server"></i> System Status</h5>
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="text-success">
                                <i class="fas fa-check-circle fa-2x"></i>
                                <div class="mt-2">Database</div>
                                <small class="text-muted">Online</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-success">
                                <i class="fas fa-check-circle fa-2x"></i>
                                <div class="mt-2">Cache</div>
                                <small class="text-muted">Active</small>
                            </div>
                        </div>
                        <div class="col-6 mt-3">
                            <div class="text-warning">
                                <i class="fas fa-exclamation-triangle fa-2x"></i>
                                <div class="mt-2">API</div>
                                <small class="text-muted">Slow</small>
                            </div>
                        </div>
                        <div class="col-6 mt-3">
                            <div class="text-success">
                                <i class="fas fa-check-circle fa-2x"></i>
                                <div class="mt-2">Storage</div>
                                <small class="text-muted">85% Free</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tips & Info -->
                <div class="recent-activity">
                    <h5><i class="fas fa-lightbulb"></i> Tips & Info</h5>
                    <div class="alert alert-info">
                        <small>
                            <strong>Tip:</strong> Gunakan cache data untuk performa yang lebih baik. 
                            Generate ulang cache jika data terlihat tidak akurat.
                        </small>
                    </div>
                    <div class="alert alert-warning">
                        <small>
                            <strong>Notice:</strong> Proses generate data besar mungkin memakan waktu. 
                            Monitor progress untuk memastikan proses berjalan lancar.
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function refreshDashboard() {
            // Add loading animation
            const btn = event.target;
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Refreshing...';
            btn.disabled = true;
            
            // Simulate refresh
            setTimeout(() => {
                location.reload();
            }, 1000);
        }

        function showSystemStatus() {
            alert('System Status:\n\n✅ Database: Online\n✅ Cache: Active\n⚠️ API: Slow Response\n✅ Storage: 85% Free\n\nAll systems operational.');
        }

        // Auto-refresh stats every 30 seconds
        setInterval(() => {
            // In real implementation, this would fetch updated stats via AJAX
            console.log('Auto-refreshing stats...');
        }, 30000);

        // Add loading animation to stats cards
        document.querySelectorAll('.stats-card').forEach(card => {
            card.addEventListener('click', function() {
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
        });
    </script>
</body>
</html>
