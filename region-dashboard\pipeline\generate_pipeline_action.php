<?php
// generate_pipeline_action.php

require_once '../../koneksi.php';

$kategori_pipeline = trim($_GET['kategori_pipeline'] ?? '1');
$kode_wilayah = $_GET['kode_wilayah'] ?? 'F';
$datedprev = $_GET['datedprev'] ?? date('Y-m-t');

switch ($kategori_pipeline) {
    case '1':
        $sql_where = "TIMESTAMPDIFF(YEAR, lw321.tgl_realisasi, lw321.next_payment) >= 1 AND lw321.kol = '1' AND lw321.flag_restruk = 'N' and (tunggakan_pokok + tunggakan_bunga = 0)";
        $kategori_label = 'Pipeline 1';
        break;
    case '2':
        $sql_where = "lw321.baki_debet <= (lw321.plafond * 0.5) AND lw321.kol = '1' AND lw321.flag_restruk = 'N' and (tunggakan_pokok + tunggakan_bunga = 0)";
        $kategori_label = 'LANCAR BERJALAN 50 PERSEN';
        break;
    case '3':
        $periode = date('Y-m-t', strtotime((date('Y') - 1) . '-12-01'));
        $sql_where = "lw321.kol = '1' AND lw321.flag_restruk = 'N' and (tunggakan_pokok + tunggakan_bunga = 0) and lw321.cif NOT IN (SELECT cif FROM lw321 WHERE periode = '$periode')";
        $kategori_label = 'Pipeline Lupus';
        break;
    default:
        $sql_where = "TIMESTAMPDIFF(YEAR, lw321.tgl_realisasi, lw321.next_payment) >= 1 AND lw321.kol = '1' AND lw321.flag_restruk = 'N'";
        $kategori_label = 'Pipeline';
        break;
}

$query = "
INSERT INTO pipeline_real_gen (
    periode, norek, cif, type, nama_debitur, region, branch, main_branch,
    plafond, baki_debet, flag_restruk, tgl_realisasi, 
    tgl_jatuh_tempo, jangka_waktu, pn_mantri, deskripsi, kategori
)
SELECT 
    lw321.periode,
    lw321.norek, 
    lw321.cif,
    lw321.type,
    lw321.nama_debitur,
    lw321.region, 
    lw321.branch, 
    lw321.main_branch, 
    lw321.plafond, 
    lw321.baki_debet, 
    lw321.flag_restruk, 
    lw321.tgl_realisasi, 
    lw321.tgl_jatuh_tempo, 
    lw321.jangka_waktu, 
    lw321.pn_mantri, 
    lw321.deskripsi, 
    '$kategori_label'
FROM lw321
WHERE $sql_where
AND periode = '$datedprev' 
AND lw321.branch IN (
    SELECT kode_uker FROM uker WHERE region = '$kode_wilayah' AND uker_type = 'un'
)
ON DUPLICATE KEY UPDATE 
    cif = VALUES(cif),
    type = VALUES(type),
    nama_debitur = VALUES(nama_debitur),
    region = VALUES(region),
    branch = VALUES(branch),
    main_branch = VALUES(main_branch),
    plafond = VALUES(plafond),
    baki_debet = VALUES(baki_debet),
    flag_restruk = VALUES(flag_restruk),
    tgl_realisasi = VALUES(tgl_realisasi),
    tgl_jatuh_tempo = VALUES(tgl_jatuh_tempo),
    jangka_waktu = VALUES(jangka_waktu),
    pn_mantri = VALUES(pn_mantri),
    deskripsi = VALUES(deskripsi),
    kategori = VALUES(kategori);
";

if (mysqli_query($koneksi, $query)) {
    echo "<div class='alert alert-success'>Pipeline <strong>$kategori_label</strong> berhasil digenerate.</div>";
} else {
    echo "<div class='alert alert-danger'>Gagal generate pipeline: " . mysqli_error($koneksi) . "</div>";
}
