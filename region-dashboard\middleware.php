<?php
session_start();

// Daftar role yang valid dengan as<PERSON><PERSON>i nilai
$valid_roles = array(
    'god' => array(
        'generate-new-npl',
        'generate-new-dpk',
        'realisasi-new',
        'jt_tempo_musiman',
        'anomali',
        'raise-unit',
        'report-pipeline',
        'report-pipeline-eksekusi',
        'kinerja_mantri',
        'generate_pipeline',
        'dashboard_pipeline',
        'realmbm',
        'real-mantri-daily',
        'tl-extra',
        'kualitas_movement',
        'mantri-rank',
        'kualitas_move_test',
        'rank-mbm',
        'dashboard_pipeline',
        'realisasi3',
        'realisasi2',
        'mantri_blm_real',
        'real-segmen2',
        'real-kur',
        'real-mantri',
        'real-mantri-daily',
        'trendh',
        'trendh-klaim',
        'rpt-ekstra',
        'pipeline',
        'tucil-rpt',
        'trendnewdpk1month',
        'test-real',
        'trend_new_dpk',
        'trend-real',
        'real_suplesi_baru',
        'rec-npl',
        'trend_newnpl',
        'bsa',
        'pergerakan_kualitas2',
        'pergerakan_kualitas',
        'realisasi',
        'npdd',
        'loan',
        'recdh',
        'recdh2',
        'npl',
        'dpk',
        'saving',
        'ldr',
        'ldr-unit',
        'tob',
        'tob-unit',
        'tob-unit-nominatif',
        'amkkm',
        'amkkm_unit',
        'brins',
        'brins_unit',
        'labaon',
        'nplunit',
        'nplunit-klasifikasi',
        'dpkunit',
        'savingunit',
        'loanunit',
        'loanunit_kup',
        'loanunit_kur',
        'loanunit_gbt',
        'loanunit_type',
        'dhunit',
        'cust',
        'saving2',
        'real',
        'eva01',
        'trendnpl',
        'loanunit2',
        'dpk3-ts',
        'ph',
        'new-tgk',
        'new-tgk-dpk',
        'npl2',
        'all-kualitas',
        'timeseries',
        'mbm-npl',
        'plar',
        'nopn',
        'dpk3',
        'dpk3-unit',
        'dpk_baru',
        'pkolek',
        'dpk3-hb',
        'smk-mbm',
        'report_rtl_all_data_bo',
        'report_rtl_all_data_uker',
        'report_rtl_all_data_mantri',
        'realisasi2',
        'trend-npl-monthly',
        'trend-dpk1',
        'trend-dpk2',
        'trend-dpk3',
        'trend-lancar',
        'pipeline',
        'penagihan-dh',
        'trend-lancar-restruk',
        'gbt-report',
        'report_rtl3',
        'report_rtl',
        'rank-unit',
        'rank-unit2',
        'rank-unit3',
        'npl_daily',
        'rank-bo',
        'timeseries-mtd',
        'labarugi',
        'labarugiunit',
        'dpk3unit',
        'dpk3',
        'dpk3-h',
        'trendnpl',
        'trendpk',
        'trend_new_dpk',
        'trend_npl_ke_pl',
        'trend_dpk_ke_l',
        'trendos',
        'trendis',
        'trendis-1',
        'trendh',
        'trendh-klaim',
        'dashscore',
        'tupok-briguna',
        'dpk3-nominatif',
        'dpk3-nominatif_3',
        'dpk3-report-nom',
        'dpk3-nominatif2',
        'bootcamp',
        'anomali',
        'aktivitas',
        'tupok',
        'report_task_force',
        'report_task_force_branch',
        'report_task_force_dpk_lunas',
        'report_task_force_npl_lunas',
        'lancar-restruk-month',
        'trend-newnpl-daily',
        'extraordinary',
        'mantri_rt',
        'index-kualitas',
        'kinerja-unit',
        'dpk1',
        'ekstra'
    ),
    'super354' => array(
        'jt_tempo_musiman',
        'anomali',
        'kinerja_mantri',
        'realmbm',
        'real-mantri-daily',
        'realisasi3',
        'realisasi2',
        'mantri_blm_real',
        'real-segmen2',
        'real-kur',
        'real-mantri',
        'trendh',
        'trendh-klaim',
        'rpt-ekstra',
        'pipeline',
        'tucil-rpt',
        'trendnewdpk1month',
        'trend-lancar-restruk',
        'test-real',
        'trend_new_dpk',
        'real_suplesi_baru',
        'rec-npl',
        'trend_newnpl',
        'bsa',
        'realisasi',
        'loan',
        'recdh',
        'recdh2',
        'npl',
        'dpk',
        'saving',
        'ldr',
        'ldr-unit',
        'amkkm',
        'amkkm_unit',
        'brins',
        'brins_unit',
        'labaon',
        'nplunit',
        'loanunit',
        'loanunit_kup',
        'dhunit',
        'real',
        'eva01',
        'trendnpl',
        'ph',
        'new-tgk',
        'new-tgk-dpk',
        'all-kualitas',
        'plar',
        'nopn',
        'dpk3',
        'dpk3-unit',
        'dpk_baru',
        'report_rtl_all_data_bo',
        'report_rtl_all_data_uker',
        'realisasi2',
        'trend-lancar',
        'pipeline',
        'penagihan-dh',
        'gbt-report',
        'report_rtl3',
        'report_rtl',
        'rank-unit',
        'rank-unit2',
        'rank-unit3',
        'npl_daily',
        'rank-bo',
        'timeseries-mtd',
        'labarugi',
        'labarugiunit',
        'dpk3unit',
        'dpk3',
        'dpk3-h',
        'trendnpl',
        'trendpk',
        'trend_new_dpk',
        'trend_npl_ke_pl',
        'trend_dpk_ke_l',
        'trendos',
        'trendis',
        'trendis-1',
        'trendh',
        'trendh-klaim',
        'dpk3-nominatif',
        'dpk3-nominatif_3',
        'dpk3-report-nom',
        'dpk3-nominatif2',
        'aktivitas',
        'report_task_force',
        'tupok',
        'index-kualitas'
    ),
    'reg' => array(
        'kinerja_mantri',
        'pipeline',
        'penagihan-dh',
        'dpk3-nominatif',
        'dpk3-nominatif_3',
        'dpk3-report-nom',
        'dpk3-nominatif2',
        'aktivitas',
        'pipeline',
        'loan',
        'recdh',
        'recdh2',
        'npl',
        'dpk',
        'saving',
        'ldr',
        'ldr-unit',
        'amkkm',
        'brins',
        'labaon',
        'ph',
        'nopn',
        'dpk3',
        'dpk3-unit',
        'dpk_baru'
    ),
    'area' => array(
        'kinerja_mantri',
        'realmbm',
        'realisasi3',
        'realisasi2',
        'real-mantri-daily',
        'mantri_blm_real',
        'real-segmen2',
        'real-kur',
        'real-mantri',
        'trendh',
        'trendh-klaim',
        'rpt-ekstra',
        'pipeline',
        'tucil-rpt',
        'trendnewdpk1month',
        'trend-lancar-restruk',
        'trend_new_dpk',
        'trend-real',
        'real_suplesi_baru',
        'rec-npl',
        'trend_newnpl',
        'bsa',
        'realisasi',
        'loan',
        'recdh',
        'recdh2',
        'npl',
        'dpk',
        'saving',
        'ldr',
        'ldr-unit',
        'amkkm',
        'amkkm_unit',
        'brins',
        'brins_unit',
        'labaon',
        'nplunit',
        'loanunit',
        'loanunit_kup',
        'dhunit',
        'trendnpl',
        'ph',
        'new-tgk',
        'new-tgk-dpk',
        'all-kualitas',
        'plar',
        'nopn',
        'dpk3',
        'dpk3-unit',
        'dpk_baru',
        'report_rtl_all_data_bo',
        'report_rtl_all_data_uker',
        'realisasi2',
        'pipeline',
        'penagihan-dh',
        'gbt-report',
        'report_rtl3',
        'report_rtl',
        'rank-unit',
        'rank-unit2',
        'rank-unit3',
        'npl_daily',
        'rank-bo',
        'timeseries-mtd',
        'labarugi',
        'labarugiunit',
        'dpk3unit',
        'dpk3',
        'dpk3-h',
        'trendnpl',
        'trendpk',
        'trend_new_dpk',
        'trend_npl_ke_pl',
        'trend_dpk_ke_l',
        'trendos',
        'trendis',
        'trendis-1',
        'trendh',
        'trendh-klaim',
        'dpk3-nominatif',
        'dpk3-nominatif_3',
        'dpk3-report-nom',
        'dpk3-nominatif2',
        'aktivitas',
        'tupok'
    ),
    'bill' => array(
        'dpk3-nominatif',
        'dpk3-nominatif_3',
        'dpk3-report-nom',
        'dpk3-nominatif2'
    )
);

// Fungsi untuk memeriksa izin akses
function checkAccess($required_mode)
{
    global $valid_roles;

    // Memeriksa apakah ada role yang aktif dalam session
    if (isset($_SESSION['role'])) {
        $active_role = $_SESSION['role'];

        // Memeriksa apakah mode yang diminta valid untuk role yang aktif
        if (in_array($required_mode, $valid_roles[$active_role])) {
            return true;  // Akses diizinkan
        } else {
            die('<h3>Akses ditolak:  tidak diizinkan</h3>');
        }
    } else {
        die('Akses ditolak: Tidak ada role yang aktif. Silakan login terlebih dahulu.');
    }
}
