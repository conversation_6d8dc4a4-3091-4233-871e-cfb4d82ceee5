<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/5.3.0/css/bootstrap.min.css" />
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css" />
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

<style>
    :root {
        --corporate-blue: #1e3a8a;
        --corporate-blue-dark: #1e40af;
        --corporate-blue-light: #3b82f6;
        --silver: #c0c0c0;
        --silver-light: #e5e7eb;
        --silver-dark: #9ca3af;
        --white: #ffffff;
        --shadow: rgba(30, 58, 138, 0.15);
    }

    .modern-navbar {
        background: transparent;
        border-radius: 15px;
        padding: 20px 25px;
        margin: 20px 0;
        position: relative;
    }

    .navbar-title {
        color: var(--corporate-blue);
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 15px;
        text-align: center;
    }

    .navbar-title i {
        color: var(--corporate-blue-light);
        margin-right: 8px;
    }

    .nav-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 12px;
        margin-top: 15px;
    }

    .nav-item {
        background: var(--white);
        border: 2px solid var(--corporate-blue);
        border-radius: 10px;
        padding: 12px 16px;
        text-decoration: none;
        color: var(--corporate-blue);
        font-weight: 600;
        font-size: 0.9rem;
        transition: all 0.3s ease;
        text-align: center;
        position: relative;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(30, 58, 138, 0.15);
    }

    .nav-item::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(30, 58, 138, 0.1), transparent);
        transition: left 0.5s ease;
    }

    .nav-item:hover::before {
        left: 100%;
    }

    .nav-item:hover {
        background: var(--corporate-blue);
        border-color: var(--corporate-blue-dark);
        transform: translateY(-3px);
        box-shadow: 0 8px 20px rgba(30, 58, 138, 0.3);
        color: var(--white);
        text-decoration: none;
    }

    .nav-item:active {
        transform: translateY(0);
    }

    .nav-item i {
        margin-right: 6px;
        color: var(--corporate-blue-light);
        font-size: 0.9rem;
    }

    .nav-item:hover i {
        color: var(--silver-light);
    }

    /* Category specific styling */
    .nav-item.npl-category {
        border-left: 4px solid #ef4444;
        background: linear-gradient(135deg, #fef2f2, #ffffff);
    }

    .nav-item.npl-category:hover {
        background: #ef4444;
        border-left-color: #dc2626;
        box-shadow: 0 8px 20px rgba(239, 68, 68, 0.4);
    }

    .nav-item.dpk-category {
        border-left: 4px solid #f59e0b;
        background: linear-gradient(135deg, #fffbeb, #ffffff);
    }

    .nav-item.dpk-category:hover {
        background: #f59e0b;
        border-left-color: #d97706;
        box-shadow: 0 8px 20px rgba(245, 158, 11, 0.4);
    }

    .nav-item.dh-category {
        border-left: 4px solid #6b7280;
        background: linear-gradient(135deg, #f9fafb, #ffffff);
    }

    .nav-item.dh-category:hover {
        background: #6b7280;
        border-left-color: #4b5563;
        box-shadow: 0 8px 20px rgba(107, 114, 128, 0.4);
    }

    .nav-item.os-category {
        border-left: 4px solid #3b82f6;
        background: linear-gradient(135deg, #eff6ff, #ffffff);
    }

    .nav-item.os-category:hover {
        background: #3b82f6;
        border-left-color: #2563eb;
        box-shadow: 0 8px 20px rgba(59, 130, 246, 0.4);
    }

    .nav-item.is-category {
        border-left: 4px solid #10b981;
        background: linear-gradient(135deg, #ecfdf5, #ffffff);
    }

    .nav-item.is-category:hover {
        background: #10b981;
        border-left-color: #059669;
        box-shadow: 0 8px 20px rgba(16, 185, 129, 0.4);
    }

    .nav-item.other-category {
        border-left: 4px solid var(--silver-dark);
        background: linear-gradient(135deg, #f8fafc, #ffffff);
    }

    .nav-item.other-category:hover {
        background: var(--silver-dark);
        border-left-color: #64748b;
        box-shadow: 0 8px 20px rgba(156, 163, 175, 0.4);
    }

    /* Responsive design */
    @media (max-width: 768px) {
        .nav-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
        }

        .nav-item {
            padding: 10px 12px;
            font-size: 0.85rem;
        }

        .navbar-title {
            font-size: 1.1rem;
            margin-bottom: 12px;
        }

        .modern-navbar {
            padding: 15px 20px;
            margin: 15px 0;
        }
    }

    @media (max-width: 480px) {
        .nav-grid {
            grid-template-columns: 1fr;
        }

        .nav-item {
            padding: 12px 16px;
        }
    }

    /* Loading animation */
    .nav-item.loading {
        pointer-events: none;
    }

    .nav-item.loading i {
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        from {
            transform: rotate(0deg);
        }

        to {
            transform: rotate(360deg);
        }
    }

    /* Pulse animation for active state */
    .nav-item.active {
        background: var(--corporate-blue);
        border-color: var(--corporate-blue-dark);
        color: var(--white);
        animation: pulse 2s infinite;
    }

    .nav-item.active i {
        color: var(--silver-light);
    }

    @keyframes pulse {
        0% {
            box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.4);
        }

        70% {
            box-shadow: 0 0 0 10px rgba(255, 255, 255, 0);
        }

        100% {
            box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
        }
    }
</style>

<div class="modern-navbar">
    <div class="navbar-title">
        <i class="fas fa-chart-line"></i>
        Trend Analysis Dashboard
    </div>

    <div class="nav-grid">
        <a href="index.php?mode=trendnpl" class="nav-item npl-category">
            <i class="fas fa-exclamation-triangle"></i>
            Trend NPL
        </a>

        <a href="index.php?mode=trend_newnpl" class="nav-item npl-category">
            <i class="fas fa-chart-area"></i>
            Trend NEW NPL
        </a>

        <a href="index.php?menu=trendpk" class="nav-item dpk-category">
            <i class="fas fa-file-invoice"></i>
            Trend SML
        </a>

        <a href="index.php?menu=trend_new_dpk" class="nav-item dpk-category">
            <i class="fas fa-file-invoice-dollar"></i>
            Trend NEW SML
        </a>

        <a href="index.php?mode=trendh" class="nav-item dh-category">
            <i class="fas fa-file-invoice-dollar"></i>
            Trend Rec DH Pokok
        </a>

        <a href="index.php?mode=trendh-klaim" class="nav-item dh-category">
            <i class="fas fa-receipt"></i>
            Trend Rec DH Klaim
        </a>

        <a href="index.php?menu=trendos" class="nav-item os-category">
            <i class="fas fa-balance-scale"></i>
            Trend OS
        </a>

        <a href="index.php?menu=trendis" class="nav-item is-category">
            <i class="fas fa-chart-bar"></i>
            Trend IS 1
        </a>

        <a href="index.php?menu=trendis-1" class="nav-item is-category">
            <i class="fas fa-chart-column"></i>
            Trend IS 2
        </a>

        <a href="index.php?menu=tupok" class="nav-item other-category">
            <i class="fas fa-arrow-trend-down"></i>
            Turun Pokok
        </a>
    </div>
</div>

<script>
    // Add loading animation when clicking nav items
    document.querySelectorAll('.nav-item').forEach(item => {
        item.addEventListener('click', function(e) {
            // Add loading state
            this.classList.add('loading');
            const icon = this.querySelector('i');
            const originalClass = icon.className;
            icon.className = 'fas fa-spinner';

            // Remove loading state after navigation (or timeout)
            setTimeout(() => {
                this.classList.remove('loading');
                icon.className = originalClass;
            }, 3000);
        });
    });

    // Highlight active menu based on current URL
    document.addEventListener('DOMContentLoaded', function() {
        const currentUrl = window.location.href;
        const navItems = document.querySelectorAll('.nav-item');

        navItems.forEach(item => {
            if (currentUrl.includes(item.getAttribute('href'))) {
                item.classList.add('active');
            }
        });
    });

    // Add ripple effect on click
    document.querySelectorAll('.nav-item').forEach(item => {
        item.addEventListener('click', function(e) {
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;

            ripple.style.cssText = `
            position: absolute;
            width: ${size}px;
            height: ${size}px;
            left: ${x}px;
            top: ${y}px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            transform: scale(0);
            animation: ripple 0.6s ease-out;
            pointer-events: none;
            z-index: 1;
        `;

            this.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });

    // Add CSS for ripple animation
    const style = document.createElement('style');
    style.textContent = `
    @keyframes ripple {
        to {
            transform: scale(2);
            opacity: 0;
        }
    }
`;
    document.head.appendChild(style);
</script>