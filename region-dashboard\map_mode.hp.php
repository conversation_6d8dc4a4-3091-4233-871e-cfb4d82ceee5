<?php
require_once '../functions.php';
// $_GET['mode'] = sanitize_variable($_GET['mode']);
// $_mode = mysqli_real_escape_string($koneksi, $_GET['mode'], ENT_QUOTES, 'UTF-8');
switch (trim($_GET['mode'])) {
    // ... rest of the code remains the same ...
    case 'realisasi-new':
        include 'real-dashboard-new.php';
        break;
        

    case 'anomali':
        include 'kualitas/anomali-dash.php';
        break;
    case 'report-pipeline':
        include 'pipeline/report-pipeline.php';
        break;
    case 'report-pipeline-eksekusi':
        include 'pipeline/report-pipeline-eksekusi.php';
        break;
    case 'kinerja_mantri':
        include 'mantri/kinerja_mantri.php';
        break;
    case 'dashboard_pipeline':
        include 'pipeline/dashboard_pipeline.php';
        break;
    case 'mantri-rank':
        include 'mantri/index_mantri.php';
        break;
    case 'realmbm':
        include 'real-dashboard-mbm.php';
        break;
    case 'realisasi3':
        include 'real-dashboard3.php';
        break;
    case 'realisasi2':
        include 'real-dashboard2.php';
        break;
    case 'mantri_blm_real':
        include 'mantri_blm_real.php';
        break;
    case 'real-segmen2':
        include 'real-daily-series2.php';
        break;
    case 'real-kur':
        include 'real-kur-series.php';
        break;
    case 'real-mantri':
        include 'real-mantri-series.php';
        break;
    case 'real-mantri-daily':
        include 'real-mantri-series-daily.php';
        break;
    case 'trendh':
        include 'trend-dh-dash.php';
        break;
    case 'trendh-klaim':
        include 'trend-dh-klaim-dash.php';
        break;
    case 'rpt-ekstra':
        include 'report_rtl4.php';
        break;
    case 'pipeline':
        include 'pipeline/list-pipeline.php';
        break;
    case 'tucil-rpt':
        include 'tuncil-series.php';
        break;
    case 'trendnewdpk1month':
        include 'trend-newdpk-1month.php';
        break;
    case 'test-real':
        include 'real-dashboard1.php';
        break;
    case 'trend_new_dpk':
        include 'trend-newdpk-dash.php';
        break;
    case 'trend-real':
        include 'trend-real.php';
        break;
    case 'real_suplesi_baru':
        include 'real-suplesi-baru.php';
        break;
    case 'rec-npl':
        include 'npl-recover-series.php';
        break;
    case 'trend_newnpl':
        include 'trend-new_npl.php';
        break;
    case 'bsa':
        include 'bsa/bsa_index.php';
        break;
    case 'pergerakan_kualitas2':
        include 'pergerakan-kualitas2.php';
        break;
    case 'pergerakan_kualitas':
        include 'pergerakan-kualitas.php';
        break;
    case 'realisasi':
        // include 'undercs.php';
        include 'real-dashboard.php';
        break;
    case 'npdd':
        // include 'undercs.php';
        include 'next_payment_real.php';
        break;
    case 'loan':
        include 'loan-dashboard.php';
        break;
    case 'recdh':
        include 'dh-dashboard.php';
        break;
    case 'recdh2':
        include 'dh-dashboard2.php';
        break;
    case 'npl':
        include 'npl-dashboard.php';
        break;
    case 'dpk':
        include 'dpk-dashboard.php';
        break;
    case 'saving':
        include 'saving-dashboard.php';
        break;
    case 'ldr':
        include 'ldr-dashboard.php';
        break;
    case 'ldr-unit':
        include 'ldr-dashboard-unit.php';
        break;
    case 'tob':
        include 'query-tob.php';
        break;
    case 'tob-unit':
        include 'query-tob-unit.php';
        break;
    case 'tob-unit-nominatif':
        include 'query-tob-unit-type.php';
        break;
    case 'amkkm':
        include 'amkkm-dash.php';
        break;
    case 'amkkm_unit':
        include 'amkkm-dash-unit.php';
        break;
    case 'brins':
        include 'brins-dash.php';
        break;
    case 'brins_unit':
        include 'brins-dash-unit.php';
        break;
    case 'labaon':
        include 'labaon-dash.php';
        break;
    case 'nplunit':
        include 'npl-dashboard-unit.php';
        break;
    case 'nplunit-klasifikasi':
        include 'npl-dashboard-klasifikasi.php';
        break;
    case 'dpkunit':
        include 'dpk-dashboard-unit.php';
        break;
    case 'savingunit':
        include 'saving-dashboard-unit.php';
        break;
    case 'loanunit':
        include 'loan-dashboard-unit.php';
        break;
    case 'loanunit_kup':
        include 'loan-dashboard-kup-unit.php';
        break;
    case 'loanunit_kur':
        include 'loan-dashboard-kur-unit.php';
        break;
    case 'loanunit_gbt':
        include 'loan-dashboard-gbt-unit.php';
        break;
    case 'loanunit_type':
        include 'loan-dash-type.php';
        break;
    case 'dhunit':
        include 'dh-dashboard-unit.php';
        break;
    case 'cust':
        include 'custom.php';
        break;
    case 'saving2':
        include 'saving-dashboard-unit2.php';
        break;
    case 'real':
        include 'real-dashboard.php';
        break;
    case 'eva01':
        include 'eva01a.php';
        break;
    case 'trendnpl':
        include 'trend-npl-dash.php';
        break;
    case 'loanunit2':
        include 'loan-dashboard-unit2.php';
        break;
    case 'dpk3-ts':
        include 'dpk3-ts-dashboard.php';
        break;
    case 'ph':
        include 'ph-dashboard.php';
        break;
    case 'new-tgk':
        include 'new-tgk-dash.php';
        break;
    case 'new-tgk-dpk':
        include 'new-tgk2-dash.php';
        break;
    case 'npl2':
        include 'npl-dashboard-2.php';
        break;
    case 'all-kualitas':
        include 'all-kualitas-dashboard.php';
        break;
    case 'timeseries':
        include 'timeseries-kualitas.php';
        break;
    case 'mbm-npl':
        include 'npl-mbm-dashboard.php';
        break;
    case 'plar':
        include 'persen-kualitas-dashboard.php';
        break;
    case 'nopn':
        include 'nopn-dash.php';
        break;
    case 'dpk3':
        include 'dpk3-ro-dashboard.php';
        break;
    case 'dpk3-unit':
        include 'dpk3-ro-unit-dashboard.php';
        break;
    case 'dpk_baru':
        include 'dpk1-ro-dashboard.php';
        break;
    case 'dpk1':
        include 'tl_dpk1.php';
        break;
    case 'pkolek':
        include 'pergerakan-kolek.php';
        break;
    case 'dpk3-hb':
        include 'dpk3-hb-dashboard.php';
        break;
    case 'smk-mbm':
        include 'mbm.php';
        break;
    case 'rpt-extra':
        include 'report-extraordinary.php';
        break;
    case 'kinerja-unit':
        include 'kinerja-dashboard-unit.php';
        break;
    case 'real-mantri-daily':
        include 'real-mantri-series-harian.php';
        break;
    case 'ekstra':
        include 'ekstra-dashboard.php';
        break;
    case 'kualitas_movement':
        include 'kualitas_movement_optimized.php';
        break;
    case 'kualitas_move_test':
        include 'kualitas_move_test.php';
        break;
}
