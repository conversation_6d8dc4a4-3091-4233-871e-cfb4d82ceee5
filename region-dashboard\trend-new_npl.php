<?php

if ($_POST['pilih-uker'] == '') {
    $uker = get_uker_region_kc('F');
    $uker2 = get_uker_region_kc('F');
} else {
    $uker = get_list_unit_active($_POST['pilih-uker']);
    $uker2 = get_list_unit_active($_POST['pilih-uker']);
}

if ($_GET['view'] == 'unit') {
    $uker = get_list_unit_active_f();
    $uker2 = get_list_unit_active_f();
}

?>

<?php


//$arr_akhir_bln = array('2024-01-31', '2024-02-29', '2024-03-31', '2024-04-30', '2024-05-31', '2024-06-30', '2024-07-31', '2024-08-31', '2024-09-30', '2024-10-31', '2024-11-30', '2024-12-31');

$satuan = 1000000;
$month = date('m');
$year = date('Y');

//

if ($_POST['year'] == '') {
    $year = date('Y');
} else {
    $year = $_POST['year'];
}

if (isset($year)) {
    $tahun = intval($year);
    $arr_akhir_bln = [];

    for ($bulan = 1; $bulan <= 12; $bulan++) {
        $tgl = date("Y-m-t", strtotime("$tahun-$bulan-01"));
        $arr_akhir_bln[] = $tgl;
    }
}

$z = 3;
for ($i = 1; $i <= $hari; $i++) {
    $arr_i[$i] = $z;
    $z++;
}
$series_hari = implode(',', $arr_i);

?>
<link rel="stylesheet" href="https://cdn.datatables.net/buttons/1.7.0/css/buttons.dataTables.min.css">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

<style>
    :root {
        --corporate-blue: #1e3a8a;
        --corporate-blue-dark: #1e40af;
        --corporate-blue-light: #3b82f6;
        --silver: #c0c0c0;
        --silver-light: #e5e7eb;
        --silver-dark: #9ca3af;
        --white: #ffffff;
        --danger: #ef4444;
        --danger-light: #fef2f2;
        --success: #10b981;
        --warning: #f59e0b;
        --shadow: rgba(30, 58, 138, 0.15);
    }

    /* Modern Container */
    .modern-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        box-shadow: 0 20px 40px var(--shadow);
        padding: 30px;
        margin: 20px 0;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    /* Page Header */
    .page-header {
        text-align: center;
        margin-bottom: 30px;
        padding: 20px 0;
    }

    .page-title {
        background: linear-gradient(135deg, var(--corporate-blue) 0%, var(--corporate-blue-light) 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 10px;
    }

    .page-subtitle {
        color: var(--silver-dark);
        font-size: 1rem;
        font-weight: 500;
    }

    /* Form Controls */
    .form-container {
        background: linear-gradient(135deg, var(--corporate-blue) 0%, var(--corporate-blue-dark) 100%);
        border-radius: 15px;
        padding: 20px;
        margin: 20px 0;
        color: white;
        box-shadow: 0 10px 30px var(--shadow);
    }

    .form-container label {
        font-weight: 600;
        margin-bottom: 8px;
        color: var(--silver-light);
    }

    .form-container select {
        background: rgba(255, 255, 255, 0.95);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 10px;
        padding: 10px 15px;
        color: var(--corporate-blue);
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .form-container select:focus {
        outline: none;
        border-color: var(--silver);
        box-shadow: 0 0 0 3px rgba(192, 192, 192, 0.3);
    }

    /* Action Buttons */
    .action-buttons {
        text-align: center;
        margin: 20px 0;
    }

    .btn-modern {
        background: linear-gradient(135deg, var(--warning) 0%, #d97706 100%);
        border: none;
        color: white;
        padding: 12px 25px;
        border-radius: 25px;
        font-weight: 600;
        font-size: 0.9rem;
        transition: all 0.3s ease;
        box-shadow: 0 5px 15px rgba(245, 158, 11, 0.3);
        text-decoration: none;
        display: inline-block;
    }

    .btn-modern:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4);
        color: white;
        text-decoration: none;
    }

    /* Modern DataTable */
    .datatable-container {
        background: white;
        border-radius: 20px;
        padding: 25px;
        box-shadow: 0 15px 35px var(--shadow);
        margin: 20px 0;
        overflow: hidden;
    }

    .datatable-header {
        display: flex;
        justify-content: between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 2px solid var(--silver-light);
    }

    .datatable-title {
        color: var(--corporate-blue);
        font-size: 1.3rem;
        font-weight: 700;
        margin: 0;
    }

    /* DataTable Styling */
    #tabel-data {
        border-collapse: separate;
        border-spacing: 0;
        width: 100% !important;
        background: white;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    }

    #tabel-data thead th {
        background: linear-gradient(135deg, var(--danger) 0%, #dc2626 100%);
        color: white;
        font-weight: 600;
        font-size: 0.9rem;
        padding: 15px 12px;
        text-align: center;
        border: none;
        position: relative;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    }

    #tabel-data thead th:first-child {
        border-top-left-radius: 15px;
    }

    #tabel-data thead th:last-child {
        border-top-right-radius: 15px;
    }

    #tabel-data tbody td {
        padding: 12px;
        border-bottom: 1px solid var(--silver-light);
        border-right: 1px solid var(--silver-light);
        transition: all 0.3s ease;
        font-size: 0.9rem;
    }

    #tabel-data tbody td:first-child {
        font-weight: 600;
        color: var(--corporate-blue);
        background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
    }

    #tabel-data tbody td:nth-child(2) {
        font-weight: 500;
        color: var(--corporate-blue-dark);
        background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
    }

    #tabel-data tbody tr:hover {
        background: linear-gradient(135deg, var(--silver-light) 0%, #f8fafc 100%);
        transform: scale(1.01);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    #tabel-data tbody tr:hover td {
        border-color: var(--corporate-blue-light);
    }

    #tabel-data tfoot th {
        background: linear-gradient(135deg, var(--danger) 0%, #dc2626 100%);
        color: white;
        font-weight: 700;
        padding: 15px 12px;
        text-align: center;
        border: none;
        font-size: 0.9rem;
    }

    #tabel-data tfoot th:first-child {
        border-bottom-left-radius: 15px;
    }

    #tabel-data tfoot th:last-child {
        border-bottom-right-radius: 15px;
    }

    /* DataTable Controls */
    .dataTables_wrapper .dataTables_length,
    .dataTables_wrapper .dataTables_filter,
    .dataTables_wrapper .dataTables_info,
    .dataTables_wrapper .dataTables_paginate {
        margin: 10px 0;
    }

    .dataTables_wrapper .dataTables_length select,
    .dataTables_wrapper .dataTables_filter input {
        border: 2px solid var(--silver-light);
        border-radius: 8px;
        padding: 8px 12px;
        margin: 0 5px;
        transition: all 0.3s ease;
    }

    .dataTables_wrapper .dataTables_length select:focus,
    .dataTables_wrapper .dataTables_filter input:focus {
        outline: none;
        border-color: var(--corporate-blue);
        box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
    }

    /* Export Buttons */
    .dt-buttons {
        margin-bottom: 20px;
    }

    .dt-button {
        background: linear-gradient(135deg, var(--corporate-blue) 0%, var(--corporate-blue-dark) 100%);
        border: none;
        color: white;
        padding: 10px 20px;
        border-radius: 8px;
        font-weight: 500;
        margin: 0 5px 5px 0;
        transition: all 0.3s ease;
        box-shadow: 0 3px 10px var(--shadow);
    }

    .dt-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px var(--shadow);
        background: linear-gradient(135deg, var(--corporate-blue-dark) 0%, var(--corporate-blue) 100%);
    }

    .dt-button:active {
        transform: translateY(0);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .modern-container {
            padding: 20px 15px;
            margin: 15px 0;
        }

        .page-title {
            font-size: 1.6rem;
        }

        .datatable-container {
            padding: 15px;
        }

        #tabel-data thead th,
        #tabel-data tbody td,
        #tabel-data tfoot th {
            padding: 8px 6px;
            font-size: 0.8rem;
        }

        .dt-button {
            padding: 8px 15px;
            font-size: 0.8rem;
        }
    }

    /* Loading Animation */
    .dataTables_processing {
        background: linear-gradient(135deg, var(--corporate-blue) 0%, var(--corporate-blue-dark) 100%);
        color: white;
        border-radius: 10px;
        border: none;
        box-shadow: 0 10px 30px var(--shadow);
    }

    /* Scrollbar Styling */
    .dataTables_scrollBody::-webkit-scrollbar {
        width: 8px;
        height: 8px;
    }

    .dataTables_scrollBody::-webkit-scrollbar-track {
        background: var(--silver-light);
        border-radius: 4px;
    }

    .dataTables_scrollBody::-webkit-scrollbar-thumb {
        background: var(--corporate-blue);
        border-radius: 4px;
    }

    .dataTables_scrollBody::-webkit-scrollbar-thumb:hover {
        background: var(--corporate-blue-dark);
    }
</style>

<script src="https://cdn.datatables.net/buttons/1.7.0/js/dataTables.buttons.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
<script src="https://cdn.datatables.net/buttons/1.7.0/js/buttons.html5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/1.7.0/js/buttons.print.min.js"></script>


<!-- Modern DataTable Initialization -->

<?php include 'menu-trends.php'; ?>

<div class="modern-container">
    <!-- Page Header -->
    <div class="page-header">
        <h1 class="page-title">
            <i class="fas fa-chart-area"></i> Trend NEW NPL Analysis
        </h1>

    </div>

    <!-- Action Buttons -->
    <div class="action-buttons">
        <a href="?mode=trend_newnpl&view=unit" class="btn-modern">
            <i class="fas fa-building"></i> Unit View
        </a>
    </div>

    <!-- Form Controls -->
    <div class="form-container">
        <form action="" method="post" class="d-flex align-items-center justify-content-center gap-3">
            <input type="hidden" name="pilih-uker" value="<?php echo $_POST['pilih-uker'] ?>">
            <label for="year" class="mb-0">
                <i class="fas fa-calendar-alt"></i> Pilih Tahun Periode:
            </label>
            <select name="year" id="year" onchange="this.form.submit()">
                <?php
                if ($_POST['year'] == '') {
                    $_POST['year'] = date('Y');
                }

                for ($y = ($year - 1); $y <= ($year + 1); $y++) {
                    if ($y == $_POST['year']) {
                        $sl = 'selected';
                    } else {
                        $sl = '';
                    }
                ?>
                    <option value="<?php echo $y ?>" <?php echo $sl ?>><?php echo $y; ?></option>
                <?php } ?>
            </select>
        </form>
    </div>
</div>

<script>
    document.title = 'Trend NEW NPL Analysis <?php echo isset($tgl_max) ? tgl_periode($tgl_max) : date('Y'); ?>'
</script>

<div class="datatable-container">
    <!-- DataTable Header -->
    <div class="datatable-header">
        <h3 class="datatable-title">
            <i class="fas fa-table"></i> Data Trend NEW NPL - Tahun <?php echo $year; ?>
        </h3>
    </div>

    <!-- DataTable -->
    <table id="tabel-data" class="table" style="width:100%" data-page-length='50'>
        <thead>
            <tr>
                <th><i class="fas fa-code"></i> BC</th>
                <th><i class="fas fa-building"></i> Branch Office</th>

                <?php
                foreach ($arr_akhir_bln as $ab) {
                ?>
                    <th><i class="fas fa-calendar"></i> <?php echo tgl_periode($ab); ?></th>
                <?php } ?>

            </tr>
        </thead>
        <tfoot>
            <th></th>
            <th><i class="fas fa-calculator"></i> TOTAL</th>

            <?php foreach ($arr_akhir_bln as $ab) { ?>
                <th></th>
            <?php } ?>
        </tfoot>
        <tbody>
            <?php
            // $sql_max_real = 'select max(periode) from l1133';
            // $rs_max = mysqli_query($koneksi, $sql_max_real);
            // $tgl_max = mysqli_fetch_array($rs_max);

            if ($_GET['view'] == 'unit') {
                $branch = "branch_code";
            } else {
                $branch = "main_branch";
            }

            while ($dat_u = mysqli_fetch_array($uker)) {
                $i++;
            ?>
                <tr>
                    <td><?php echo $dat_u['kode_uker'] ?></td>
                    <td><?php echo $dat_u['nama_uker'] ?></td>

                    <?php
                    foreach ($arr_akhir_bln as $ab) {
                        $periode_ = $ab;
                        $uker_type = cek_branch2($dat_u['kode_uker']);
                        if ($uker_type == 'ro') {
                            $branch = "main_branch";
                        } else if ($uker_type == 'kc') {
                            $branch = "main_branch";
                        } else if ($uker_type == 'un') {
                            $branch = "branch_code";
                        }
                        $sql_new_npl = "select SUM(new_npl_os) as os, SUM(new_npl_deb) as deb from new_npl_cache where $branch = '" . $dat_u['kode_uker'] . "' and periode = '$periode_' and branch_code <> main_branch";
                        // echo $sql_new_npl;
                        $rs_new_npl = mysqli_query($koneksi, $sql_new_npl);
                        $npl_periode = mysqli_fetch_array($rs_new_npl);

                        // $npl_periode = new_npl($dat_u['kode_uker'], $periode_);


                    ?>

                        <td><?php  // echo $mtd_npl;
                            echo $npl_periode['os'];
                            ?></td>
                    <?php } ?>
                </tr>
            <?php } ?>
        </tbody>
    </table>
</div>

<script>
    // Enhanced DataTable initialization with modern features
    $(document).ready(function() {
        // Add loading overlay
        $('body').append('<div id="loadingOverlay" style="display:none; position:fixed; top:0; left:0; width:100%; height:100%; background:rgba(30,58,138,0.8); z-index:9999; color:white; display:flex; align-items:center; justify-content:center; font-size:1.2rem;"><i class="fas fa-spinner fa-spin fa-2x"></i><span style="margin-left:15px;">Loading data...</span></div>');

        // Show loading
        $('#loadingOverlay').show();

        // Initialize DataTable with enhanced features
        setTimeout(function() {
            try {
                // Check if table exists
                if ($('#tabel-data').length === 0) {
                    console.error('Table #tabel-data not found');
                    $('#loadingOverlay').hide();
                    return;
                }

                // Check if DataTables is loaded
                if (typeof $.fn.DataTable === 'undefined') {
                    console.error('DataTables library not loaded');
                    $('#loadingOverlay').hide();
                    return;
                }

                $('#tabel-data').DataTable({
                    paging: false,
                    autoWidth: true,
                    processing: true,
                    language: {
                        processing: '<i class="fas fa-spinner fa-spin"></i> Processing data...',
                        search: 'Search data:',
                        lengthMenu: 'Show _MENU_ entries per page',
                        info: 'Showing _START_ to _END_ of _TOTAL_ entries',
                        infoEmpty: 'No data available',
                        infoFiltered: '(filtered from _MAX_ total entries)',
                        zeroRecords: 'No matching records found',
                        emptyTable: 'No data available in table'
                    },
                    footerCallback: function(row, data, start, end, display) {
                        var api = this.api();
                        var nb_cols = api.columns().nodes().length;

                        // Fungsi konversi string ke angka
                        var intVal = function(i) {
                            return typeof i === 'string' ?
                                i.replace(/[\$,]/g, '') * 1 :
                                typeof i === 'number' ?
                                i : 0;
                        };

                        // Loop mulai dari kolom ke-2 (index 2)
                        for (var j = 2; j < nb_cols; j++) {
                            var pageTotal = api
                                .column(j, {
                                    page: 'current'
                                })
                                .data()
                                .reduce(function(a, b) {
                                    return intVal(a) + intVal(b);
                                }, 0);

                            // Format ke en-US: koma ribuan, titik desimal
                            var formattedTotal = new Intl.NumberFormat('en-US', {
                                minimumFractionDigits: 0,
                                maximumFractionDigits: 0
                            }).format(pageTotal);

                            $(api.column(j).footer()).html('<strong>' + formattedTotal + '</strong>');
                        }
                    },

                    dom: 'Bfrtip',
                    buttons: [{
                            extend: 'copyHtml5',
                            footer: true,
                            text: '<i class="fas fa-copy"></i> Copy',
                            className: 'dt-button'
                        },
                        {
                            extend: 'excelHtml5',
                            footer: true,
                            text: '<i class="fas fa-file-excel"></i> Excel',
                            className: 'dt-button',
                            title: 'Trend NEW NPL - ' + <?php echo json_encode($year); ?>
                        },
                        {
                            extend: 'csvHtml5',
                            footer: true,
                            text: '<i class="fas fa-file-csv"></i> CSV',
                            className: 'dt-button'
                        },
                        {
                            text: '<i class="fas fa-sync"></i> Refresh',
                            className: 'dt-button',
                            action: function(e, dt, node, config) {
                                location.reload();
                            }
                        }
                    ],

                    // Terapkan class text-end ke kolom angka
                    columnDefs: [{
                            targets: [0], // Branch code column
                            className: "text-center"
                        },
                        {
                            targets: [1], // Branch name column
                            className: "text-start"
                        },
                        {
                            targets: [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13], // Data columns
                            className: "text-end",
                            render: function(data, type, row) {
                                if (type === 'display' && data && !isNaN(data)) {
                                    return new Intl.NumberFormat('en-US', {
                                        minimumFractionDigits: 0,
                                        maximumFractionDigits: 0
                                    }).format(data);
                                }
                                return data;
                            }
                        }
                    ],

                    // Add row callback for enhanced styling
                    rowCallback: function(row, data, index) {
                        // Add hover effect data
                        $(row).attr('data-index', index);

                        // Highlight high values
                        if (data && data.length > 2) {
                            for (var i = 2; i < data.length; i++) {
                                if (data[i] && data[i] !== '') {
                                    var value = parseInt(data[i].toString().replace(/[,\s]/g, ''));
                                    if (!isNaN(value) && value > 1000000) { // 1M threshold
                                        $(row).find('td:eq(' + i + ')').addClass('high-value');
                                    }
                                }
                            }
                        }
                    },

                    initComplete: function() {
                        // Hide loading overlay
                        $('#loadingOverlay').hide();

                        // Add search enhancement
                        setTimeout(function() {
                            $('.dataTables_filter input').attr('placeholder', 'Search branches or values...');
                        }, 100);

                        // Add summary info
                        var totalRows = this.api().rows().count();
                        if ($('.datatable-header').length > 0) {
                            $('.datatable-header').append(
                                '<div class="table-summary"><i class="fas fa-info-circle"></i> ' +
                                totalRows + ' branches loaded</div>'
                            );
                        }

                        // Add export info
                        setTimeout(function() {
                            if ($('.dt-buttons').length > 0) {
                                $('.dt-buttons').prepend('<span class="export-label"><i class="fas fa-download"></i> Export: </span>');
                            }
                        }, 200);
                    }
                });

                // Add custom CSS for high values
                $('<style>')
                    .prop('type', 'text/css')
                    .html(
                        '.high-value {' +
                        '    background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%) !important;' +
                        '    color: #dc2626 !important;' +
                        '    font-weight: 600 !important;' +
                        '}' +
                        '.table-summary {' +
                        '    color: #6b7280;' +
                        '    font-size: 0.9rem;' +
                        '    margin-left: auto;' +
                        '}' +
                        '.export-label {' +
                        '    color: #374151;' +
                        '    font-weight: 600;' +
                        '    margin-right: 10px;' +
                        '}'
                    )
                    .appendTo('head');

            } catch (error) {
                console.error('Error initializing DataTable:', error);
                $('#loadingOverlay').hide();

                // Fallback: Apply basic styling if DataTable fails
                $('#tabel-data').addClass('table table-striped table-bordered');
                $('#tabel-data thead').addClass('table-danger');
                $('#tabel-data tfoot').addClass('table-danger');

                // Show error message
                $('.datatable-container').prepend(
                    '<div class="alert alert-warning">' +
                    '<i class="fas fa-exclamation-triangle"></i> ' +
                    'Advanced table features could not be loaded. Basic table view is shown.' +
                    '</div>'
                );
            }
        }, 500); // Small delay to show loading effect
    });

    // Add keyboard shortcuts
    $(document).keydown(function(e) {
        // Ctrl+F for search
        if (e.ctrlKey && e.keyCode === 70) {
            e.preventDefault();
            $('.dataTables_filter input').focus();
        }

        // Ctrl+E for Excel export
        if (e.ctrlKey && e.keyCode === 69) {
            e.preventDefault();
            $('.buttons-excel').click();
        }
    });

    // Add tooltip for buttons
    $(document).ready(function() {
        setTimeout(function() {
            $('.dt-button').each(function() {
                var text = $(this).text().trim();
                $(this).attr('title', 'Click to ' + text.toLowerCase());
            });
        }, 1000);
    });
</script>