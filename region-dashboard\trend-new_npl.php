<?php

if ($_POST['pilih-uker'] == '') {
    $uker = get_uker_region_kc('F');
    $uker2 = get_uker_region_kc('F');
} else {
    $uker = get_list_unit_active($_POST['pilih-uker']);
    $uker2 = get_list_unit_active($_POST['pilih-uker']);
}

if ($_GET['view'] == 'unit') {
    $uker = get_list_unit_active_f();
    $uker2 = get_list_unit_active_f();
}

?>

<?php


//$arr_akhir_bln = array('2024-01-31', '2024-02-29', '2024-03-31', '2024-04-30', '2024-05-31', '2024-06-30', '2024-07-31', '2024-08-31', '2024-09-30', '2024-10-31', '2024-11-30', '2024-12-31');

$satuan = 1000000;
$month = date('m');
$year = date('Y');

//

if ($_POST['year'] == '') {
    $year = date('Y');
} else {
    $year = $_POST['year'];
}

if (isset($year)) {
    $tahun = intval($year);
    $arr_akhir_bln = [];

    for ($bulan = 1; $bulan <= 12; $bulan++) {
        $tgl = date("Y-m-t", strtotime("$tahun-$bulan-01"));
        $arr_akhir_bln[] = $tgl;
    }
}

$z = 3;
for ($i = 1; $i <= $hari; $i++) {
    $arr_i[$i] = $z;
    $z++;
}
$series_hari = implode(',', $arr_i);

?>
<link rel="stylesheet" href="https://cdn.datatables.net/buttons/1.7.0/css/buttons.dataTables.min.css">

<script src="https://cdn.datatables.net/buttons/1.7.0/js/dataTables.buttons.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
<script src="https://cdn.datatables.net/buttons/1.7.0/js/buttons.html5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/1.7.0/js/buttons.print.min.js"></script>


<!-- fungsi datatable -->

<script>
    $(document).ready(function() {
        $('#tabel-data').DataTable({
            paging: false,
            autoWidth: true,
            footerCallback: function(row, data, start, end, display) {
                var api = this.api();
                var nb_cols = api.columns().nodes().length;

                // Fungsi konversi string ke angka
                let intVal = function(i) {
                    return typeof i === 'string' ?
                        i.replace(/[\$,]/g, '') * 1 :
                        typeof i === 'number' ?
                        i : 0;
                };

                // Loop mulai dari kolom ke-2 (index 2)
                for (var j = 2; j < nb_cols; j++) {
                    var pageTotal = api
                        .column(j, {
                            page: 'current'
                        })
                        .data()
                        .reduce(function(a, b) {
                            return intVal(a) + intVal(b);
                        }, 0);

                    // Format ke en-US: koma ribuan, titik desimal
                    var formattedTotal = new Intl.NumberFormat('en-US', {
                        minimumFractionDigits: 0,
                        maximumFractionDigits: 0
                    }).format(pageTotal);

                    $(api.column(j).footer()).html(formattedTotal);
                }
            },

            dom: 'Bfrtip',
            buttons: [{
                    extend: 'copyHtml5',
                    footer: true
                },
                {
                    extend: 'excelHtml5',
                    footer: true
                },
                {
                    extend: 'csvHtml5',
                    footer: true
                },
            ],

            // Terapkan class text-end ke kolom angka (misalnya mulai dari kolom index 2 ke atas)
            columnDefs: [{
                targets: [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15,16,17,18,19,20,21,22,23,24,25,26,27,28], // Atau: [2,3,4,5] untuk kolom tertentu
                className: "text-end",
                render: $.fn.dataTable.render.number(',', '.', 0)
            }]
        });
    });
</script>

<?php include 'menu-trends.php'; ?>
<h5 class="text-center">Trend NEW NPL (Rp)</h5>
<div class="text-center">
    <a href="?mode=trend_newnpl&view=unit"><button class="button btn-warning text-dark">Unit View</button></a>
</div>
<script>
    document.title = 'Series NEW NPL <?php echo tgl_periode($tgl_max); ?>'
</script>
<p class="text-center">
<div class="container-fluid">
    <form action="" method="post">
        <input type="hidden" name="pilih-uker" value="<?php echo $_POST['pilih-uker'] ?>">
        <label>Pilih Tahun Periode : </label>
        <select name="year" onchange="this.form.submit()">
            <?php
            if ($_POST['year'] == '') {
                $_POST['year'] = date('Y');
            }

            for ($y = ($year - 1); $y <= ($year + 1); $y++) {
                if ($y == $_POST['year']) {
                    $sl = 'selected';
                } else {
                    $sl = '';
                }
            ?>
                <option value="<?php echo $y ?>" <?php echo $sl ?>><?php echo $y; ?></option>
            <?php } ?>
        </select>
    </form>
</div>
</p>

<table id="tabel-data" class="table table-striped table-bordered" style="width:100%" data-page-length='50'>
    <thead class="table-danger">
        <tr>
            <th>BC</th>
            <th>Branch Office</th>

            <?php
            foreach ($arr_akhir_bln as $ab) {
            ?>
                <th colspan="2"><?php echo tgl_periode($ab); ?></th>
            <?php } ?>

        </tr>
    </thead>
    <tfoot class="table-danger">
        <th></th>
        <th>TOTAL</th>


        <?php foreach ($arr_akhir_bln as $ab) { ?>
            <th></th>
        <?php } ?>
    </tfoot>
    <tbody>
        <?php
        // $sql_max_real = 'select max(periode) from l1133';
        // $rs_max = mysqli_query($koneksi, $sql_max_real);
        // $tgl_max = mysqli_fetch_array($rs_max);

        if ($_GET['view'] == 'unit') {
            $branch = "branch_code";
        } else {
            $branch = "main_branch";
        }

        while ($dat_u = mysqli_fetch_array($uker)) {
            $i++;
        ?>
            <tr>
                <td><?php echo $dat_u['kode_uker'] ?></td>
                <td><?php echo $dat_u['nama_uker'] ?></td>

                <?php
                foreach ($arr_akhir_bln as $ab) {
                    $periode_ = $ab;
                    $uker_type = cek_branch2($dat_u['kode_uker']);
                    if ($uker_type == 'ro') {
                        $branch = "main_branch";
                    } else if ($uker_type == 'kc') {
                        $branch = "main_branch";
                    } else if ($uker_type == 'un') {
                        $branch = "branch_code";
                    }
                    $sql_new_npl = "select SUM(new_npl_os) as os, SUM(new_npl_deb) as deb from new_npl_cache where $branch = '" . $dat_u['kode_uker'] . "' and periode = '$periode_' and branch_code <> main_branch";
                    // echo $sql_new_npl;
                    $rs_new_npl = mysqli_query($koneksi, $sql_new_npl);
                    $npl_periode = mysqli_fetch_array($rs_new_npl);
                        
                    // $npl_periode = new_npl($dat_u['kode_uker'], $periode_);


                ?>

                    <td><?php  // echo $mtd_npl;
                        echo $npl_periode['deb'];
                        ?></td>
                    <td><?php  // echo $mtd_npl;
                        echo $npl_periode['os'];
                        ?></td>
                <?php } ?>
            </tr>
        <?php } ?>
    </tbody>
</table>