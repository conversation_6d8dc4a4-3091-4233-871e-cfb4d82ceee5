<?php
include '../../koneksi.php'; // koneksi ke DB

$periode_awal = $_GET['periode_awal'];
$periode_akhir = $_GET['periode_akhir'];

$sql = "
SELECT 
    a.Nomor_Rekening,
    a.<PERSON> AS kol_awal,
    b.Ko<PERSON> AS kol_akhir,
    a.Periode AS periode_awal,
    b.Periode AS periode_akhir,
    CASE
        WHEN b.Nomor_Rekening IS NULL THEN 'Lunas'
        WHEN a.Kolek = 1 AND b.<PERSON> = 2 THEN 'New DPK'
        WHEN a.Kolek = 1 AND b.<PERSON> > 2 THEN 'New NPL'
        WHEN a.Kolek = 2 AND b.Kolek = 1 THEN 'Recovery DPK → Lancar'
        WHEN a.Kolek > 2 AND b.Kolek = 2 THEN 'Recovery NPL → DPK'
        WHEN a.Kolek > 2 AND b.Kolek = 1 THEN 'Recovery NPL → Lancar'
        ELSE 'Tetap'
    END AS movement
FROM mapping_kualitas a
LEFT JOIN mapping_kualitas b
    ON a.Nomor_Rekening = b.Nomor_Rekening AND b.Periode = '$periode_akhir'
WHERE a.Periode = '$periode_awal'
";

$result = mysqli_query($koneksi, $sql);

$data = [];
$summary = [];

while ($row = mysqli_fetch_assoc($result)) {
    if (!isset($summary[$row['movement']])) {
        $summary[$row['movement']] = 0;
    }
    $summary[$row['movement']]++;

    if ($row['movement'] !== 'Tetap') {
        $data[] = $row;
    }
}

echo json_encode([
    'nominatif' => $data,
    'summary' => $summary
]);
