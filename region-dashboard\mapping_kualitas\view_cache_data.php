<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>View NEW NPL Cache Data</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .header-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .stats-card {
            background: white;
            border-radius: 10px;
            padding: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .table-container {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body class="bg-light">
    <?php
    require_once '../koneksi.php';
    
    // Get cache statistics
    $stats_sql = "SELECT 
        COUNT(*) as total_records,
        COUNT(DISTINCT periode) as total_periods,
        COUNT(DISTINCT branch_code) as total_branches,
        MIN(periode) as earliest_periode,
        MAX(periode) as latest_periode,
        SUM(new_npl_os) as total_os,
        SUM(new_npl_deb) as total_deb,
        MAX(updated_at) as last_update
        FROM new_npl_cache";
    
    $stats_result = mysqli_query($koneksi, $stats_sql);
    $stats = mysqli_fetch_assoc($stats_result);
    
    // Get filter parameters
    $selected_periode = $_GET['periode'] ?? '';
    $selected_region = $_GET['region'] ?? '';
    $selected_branch = $_GET['branch'] ?? '';
    
    // Get available periods
    $periods_sql = "SELECT DISTINCT periode FROM new_npl_cache ORDER BY periode DESC";
    $periods_result = mysqli_query($koneksi, $periods_sql);
    $periods = [];
    while ($row = mysqli_fetch_assoc($periods_result)) {
        $periods[] = $row['periode'];
    }
    
    // Get available regions
    $regions_sql = "SELECT DISTINCT region FROM new_npl_cache ORDER BY region";
    $regions_result = mysqli_query($koneksi, $regions_sql);
    $regions = [];
    while ($row = mysqli_fetch_assoc($regions_result)) {
        $regions[] = $row['region'];
    }
    ?>

    <div class="container-fluid mt-4">
        <!-- Header -->
        <div class="header-card text-center">
            <h2><i class="fas fa-database"></i> NEW NPL Cache Data Viewer</h2>
            <p class="mb-0">View and analyze cached NEW NPL data</p>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-2">
                <div class="stats-card text-center">
                    <h4 class="text-primary"><?php echo number_format($stats['total_records'] ?? 0); ?></h4>
                    <small>Total Records</small>
                </div>
            </div>
            <div class="col-md-2">
                <div class="stats-card text-center">
                    <h4 class="text-success"><?php echo $stats['total_periods'] ?? 0; ?></h4>
                    <small>Periods</small>
                </div>
            </div>
            <div class="col-md-2">
                <div class="stats-card text-center">
                    <h4 class="text-info"><?php echo $stats['total_branches'] ?? 0; ?></h4>
                    <small>Branches</small>
                </div>
            </div>
            <div class="col-md-2">
                <div class="stats-card text-center">
                    <h4 class="text-warning"><?php echo number_format(($stats['total_os'] ?? 0) / 1000000, 2); ?>M</h4>
                    <small>Total OS (Rp)</small>
                </div>
            </div>
            <div class="col-md-2">
                <div class="stats-card text-center">
                    <h4 class="text-danger"><?php echo number_format($stats['total_deb'] ?? 0); ?></h4>
                    <small>Total DEB</small>
                </div>
            </div>
            <div class="col-md-2">
                <div class="stats-card text-center">
                    <h6 class="text-muted"><?php echo $stats['last_update'] ? date('d/m/Y H:i', strtotime($stats['last_update'])) : 'Never'; ?></h6>
                    <small>Last Update</small>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="table-container mb-4">
            <h5><i class="fas fa-filter"></i> Filters</h5>
            <form method="GET" class="row g-3">
                <div class="col-md-4">
                    <label class="form-label">Periode:</label>
                    <select name="periode" class="form-select" onchange="this.form.submit()">
                        <option value="">All Periods</option>
                        <?php foreach ($periods as $periode): ?>
                            <option value="<?php echo $periode; ?>" <?php echo $periode == $selected_periode ? 'selected' : ''; ?>>
                                <?php echo date('F Y', strtotime($periode)); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-4">
                    <label class="form-label">Region:</label>
                    <select name="region" class="form-select" onchange="this.form.submit()">
                        <option value="">All Regions</option>
                        <?php foreach ($regions as $region): ?>
                            <option value="<?php echo $region; ?>" <?php echo $region == $selected_region ? 'selected' : ''; ?>>
                                <?php echo $region; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-4">
                    <label class="form-label">Branch Code:</label>
                    <input type="text" name="branch" class="form-control" value="<?php echo htmlspecialchars($selected_branch); ?>" 
                           placeholder="Enter branch code" onchange="this.form.submit()">
                </div>
            </form>
        </div>

        <!-- Data Table -->
        <div class="table-container">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5><i class="fas fa-table"></i> Cache Data</h5>
                <div>
                    <button class="btn btn-success btn-sm" onclick="exportData()">
                        <i class="fas fa-file-excel"></i> Export Excel
                    </button>
                    <button class="btn btn-primary btn-sm" onclick="refreshData()">
                        <i class="fas fa-sync"></i> Refresh
                    </button>
                </div>
            </div>

            <div class="table-responsive">
                <table id="cacheTable" class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>Periode</th>
                            <th>Branch Code</th>
                            <th>Branch Name</th>
                            <th>Region</th>
                            <th>Main Branch</th>
                            <th class="text-end">New NPL OS (Rp)</th>
                            <th class="text-end">New NPL DEB</th>
                            <th class="text-end">Avg per DEB</th>
                            <th>Updated At</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        // Build WHERE clause based on filters
                        $where_conditions = [];
                        if ($selected_periode) {
                            $where_conditions[] = "periode = '" . mysqli_real_escape_string($koneksi, $selected_periode) . "'";
                        }
                        if ($selected_region) {
                            $where_conditions[] = "region = '" . mysqli_real_escape_string($koneksi, $selected_region) . "'";
                        }
                        if ($selected_branch) {
                            $where_conditions[] = "branch_code LIKE '%" . mysqli_real_escape_string($koneksi, $selected_branch) . "%'";
                        }
                        
                        $where_clause = !empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "";
                        
                        $data_sql = "SELECT * FROM new_npl_cache $where_clause ORDER BY periode DESC, new_npl_os DESC LIMIT 1000";
                        $data_result = mysqli_query($koneksi, $data_sql);
                        
                        while ($row = mysqli_fetch_assoc($data_result)):
                            $avg_per_deb = $row['new_npl_deb'] > 0 ? $row['new_npl_os'] / $row['new_npl_deb'] : 0;
                        ?>
                        <tr>
                            <td><?php echo date('M Y', strtotime($row['periode'])); ?></td>
                            <td><?php echo $row['branch_code']; ?></td>
                            <td><?php echo $row['branch_name']; ?></td>
                            <td><?php echo $row['region']; ?></td>
                            <td><?php echo $row['main_branch']; ?></td>
                            <td class="text-end"><?php echo number_format($row['new_npl_os']); ?></td>
                            <td class="text-end"><?php echo number_format($row['new_npl_deb']); ?></td>
                            <td class="text-end"><?php echo number_format($avg_per_deb); ?></td>
                            <td><?php echo date('d/m/Y H:i', strtotime($row['updated_at'])); ?></td>
                        </tr>
                        <?php endwhile; ?>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Period Range Info -->
        <div class="stats-card mt-4">
            <div class="row text-center">
                <div class="col-md-6">
                    <strong>Period Range:</strong> 
                    <?php echo $stats['earliest_periode'] ? date('M Y', strtotime($stats['earliest_periode'])) : 'N/A'; ?> 
                    to 
                    <?php echo $stats['latest_periode'] ? date('M Y', strtotime($stats['latest_periode'])) : 'N/A'; ?>
                </div>
                <div class="col-md-6">
                    <strong>Data Freshness:</strong> 
                    <?php 
                    if ($stats['last_update']) {
                        $last_update = strtotime($stats['last_update']);
                        $hours_ago = round((time() - $last_update) / 3600, 1);
                        echo $hours_ago < 24 ? "$hours_ago hours ago" : round($hours_ago / 24, 1) . " days ago";
                    } else {
                        echo "No data";
                    }
                    ?>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.2.2/js/dataTables.buttons.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.html5.min.js"></script>

    <script>
        $(document).ready(function() {
            $('#cacheTable').DataTable({
                pageLength: 50,
                order: [[0, 'desc'], [5, 'desc']], // Order by periode desc, then by OS desc
                dom: 'Bfrtip',
                buttons: [
                    'copy', 'excel', 'pdf'
                ],
                language: {
                    search: "Search:",
                    lengthMenu: "Show _MENU_ entries",
                    info: "Showing _START_ to _END_ of _TOTAL_ entries",
                    paginate: {
                        first: "First",
                        last: "Last",
                        next: "Next",
                        previous: "Previous"
                    }
                }
            });
        });

        function exportData() {
            $('#cacheTable').DataTable().button('.buttons-excel').trigger();
        }

        function refreshData() {
            location.reload();
        }
    </script>
</body>
</html>
