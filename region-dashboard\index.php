<?php
session_start();
header('X-Frame-Options: SAMEORIGIN');
header('X-Content-Type-Options: "nosniff"');
header('X-XSS-Protection: 1; mode=block');
header('Strict-Transport-Security: max-age=63072000; includeSubDomains; preload');
// header("Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline';");
header('Referrer-Policy: no-referrer');
header('Permissions-Policy: geolocation=(self), camera=(), microphone=(), fullscreen=(self https://brilianboard.id)');
header('Cross-Origin-Opener-Policy: same-origin');
header('Cross-Origin-Resource-Policy: same-origin');
// header('Cross-Origin-Embedder-Policy: require-corp');
require_once '../koneksi.php';
require_once '../functions.php';
require_once 'region-function.php';
ini_set('session.cookie_httponly', 1); // mencegah akses cookie via JS
ini_set('session.cookie_secure', 1);   // wajib jika pakai HTTPS
ini_set('session.use_only_cookies', 1);
$kode_wilayah = getRegion($_SESSION['kodeuker']);

function regenerate_session()
{
    if (!isset($_SESSION['regenerate_time'])) {
        $_SESSION['regenerate_time'] = time();
    }  // Regenerasi ID sesi setiap 10 menit
    if (time() - $_SESSION['regenerate_time'] > 600) {
        session_regenerate_id(true);
        $_SESSION['regenerate_time'] = time();
    }
}

$kode_wilayah = getRegion($_SESSION['kodeuker']);

regenerate_session();
session_write_close();
if ($_GET['menu'] != '')
    $required_mode = $_GET['menu'];

if ($_GET['mode'] != '')
    $required_mode = $_GET['mode'];

if ($required_mode != '') {
    checkAccess($required_mode);
}
$auth_role = array('reg', 'super354', 'god', 'area');
if ($required_mode == '') {
    if (isset($_SESSION['role']) && !in_array($_SESSION['role'], $auth_role)) {
        // Kode yang akan dijalankan jika role tidak ada dalam array
        die('NOT authorized!!');
    }
}

// if ($_SESSION['active'] == 'N' && ($_SESSION['role'] == 'super354' || $_SESSION['role'] == 'reg')) {
//     header('location: ../menu-account.php');
// }
$satuan = 1000000;
$arr_month = array('1' => 'Jan', 'Feb', 'Mar', 'Apr', 'Mei', 'Jun', 'Jul', 'Agt', 'Sept', 'Okt', 'Nov', 'Des');
// print_r($array_month);
$arr_area = array(1 => 'Mikro Bandung Metro Area', 'Non Metro Area');
$arr_dates = array('0' => 'Des', 'Jan', 'Feb', 'Mar', 'Apr', 'Mei', 'Jun', 'Jul', 'Agt', 'Sept', 'Okt', 'Nov', 'Des');

if ($_SESSION['role'] == 'super354') {
    $str = "main_branch = '" . $_SESSION['kodeuker'] . "'";
} else if ($_SESSION['role'] == 'god') {
    $str = "region = '" . $kode_wilayah . "'";
} else if ($_SESSION['role'] == 'god' && $_POST['pilih-area'] != '') {
    $str = "id_area = '" . $_POST['pilih-area'] . "'";
} else if ($_SESSION['role'] == 'area') {
    $str = "region = '" . $kode_wilayah . "' and id_area = '" . $_SESSION['kodeuker'] . "'";
} else {
    $str = "branch = '" . $_SESSION['kodeuker'] . "'";
}

// print_r($_SESSION);

// print_r($_POST);

$cek = cek_branch2($_SESSION['kodeuker']);
// echo $cek;
$branch = $_SESSION['kodeuker'];
// cek max periode l1133
$cekmax  = mysqli_query($koneksi, 'select MAX(periode) from l1133');
$dmax    = mysqli_fetch_row($cekmax);
$tgl_max = $dmax[0];

// cek max periode neraca
$cekmax_neraca  = mysqli_query($koneksi, 'select MAX(periode) from neraca');
$dmax_neraca    = mysqli_fetch_row($cekmax_neraca);
$tgl_max_neraca = $dmax_neraca[0];

// cek max periode lw321
$cekmax_lw321  = mysqli_query($koneksi, 'select MAX(periode) from lw321');
$dmax_lw321    = mysqli_fetch_row($cekmax_lw321);
$tgl_max_lw321 = $dmax_lw321[0];

date_default_timezone_set('Asia/Jakarta'); // Atur zona waktu ke Jakarta
$tanggal_hari_ini = date('Y-m-d', strtotime('-1 day'));

// Cek mana yang belum update
$belum_update = [];
if ($tgl_max != $tanggal_hari_ini) $belum_update[]        = 'L1133';
if ($tgl_max_neraca != $tanggal_hari_ini) $belum_update[] = 'Gi405';
if ($tgl_max_lw321 != $tanggal_hari_ini) $belum_update[]  = 'LW321';

// Tampilkan modal kalo ada yang belum update
$show_modal_update = count($belum_update) > 0;


$judul_modal = '';
$isi_modal   = '';

$_SESSION['notif'] = 0;

if ($show_modal_update) {

    $_SESSION['notif']++;

    if (count($belum_update) == 1) {
        $judul_modal = "{$belum_update[0]} Belum Update!";
        $tanggal_terakhir = strtolower($belum_update[0]) == 'l1133' ? $tgl_max : (strtolower($belum_update[0]) == 'neraca' ? $tgl_max_neraca : $tgl_max_lw321);

        $isi_modal = "Periode terakhir adalah <strong>" .
            htmlspecialchars(date('d-m-Y', strtotime($tanggal_terakhir))) .
            "</strong>.<br>Sedang menunggu update data dari brisim untuk periode <strong>" .
            date('d-m-Y', strtotime($tanggal_hari_ini)) . "</strong>.";
    } else {
        $judul_modal = implode(', ', $belum_update) . " Belum Update!";
        $tanggal_terakhir = strtolower($belum_update[0]) == 'l1133' ? $tgl_max : (strtolower($belum_update[0]) == 'neraca' ? $tgl_max_neraca : $tgl_max_lw321);

        $isi_modal = "Periode terakhir adalah <strong>" .
            htmlspecialchars(date('d-m-Y', strtotime($tanggal_terakhir))) .
            "</strong>.<br>Sedang menunggu update data dari brisim untuk periode <strong>" .
            date('d-m-Y', strtotime($tanggal_hari_ini)) . "</strong>.";
    }
}

if ($_GET['pilih-area'] != '') {
    $_POST['pilih-area'] = $_GET['pilih_area'];
}

if ($_POST['pilih_periode'] != '') {
    $tgl_max = $_POST['pilih_periode'];
}

if ($_SESSION['role'] == 'super354') {
    $main_branch = $_SESSION['kodeuker'];
} else {
    $branch = $_SESSION['kodeuker'];
}

// $dtrx = date_create($tgl_max);
// $tgl_max = '2024-02-15';
// echo dtrx;
$dt = explode('-', $tgl_max);
// $
$list_date = 'select distinct periode from neraca order by periode desc';
$stmt = $mysqli->prepare($list_date);
$stmt->execute();
$rs_list_date = $stmt->get_result();

$datedprev = date('Y-m-d', strtotime('last day of previous month', mktime(0, 0, 0, $dt[1], $dt[2], $dt[0])));
// $prevyear = date('Y-m-d', strtotime('last year', mktime(0, 0, 0, $dt[1], $dt[2], $dt[0])));
$previous_year = $dt[0] - 1;
$prevyear = $previous_year . '-12-31';
$lastday = date('Y-m-d', strtotime('yesterday', mktime(0, 0, 0, $dt[1], $dt[2], $dt[0])));
$lastfriday = date('Y-m-d', strtotime('last friday', mktime(0, 0, 0, $dt[1], $dt[2], $dt[0])));
$today = date('Y-m-d', strtotime('today', mktime(0, 0, 0, $dt[1], $dt[2], $dt[0])));
$mom_date = date('Y-m-d', strtotime('-1 month', mktime(0, 0, 0, $dt[1], $dt[2], $dt[0])));
$date_rka = $dt[0] . '-' . $dt[1] . '-01';
$date_rka_des = $dt[0] . '-12-01';
$dt2 = explode('-', $datedprev);
$date_rka_prev = $dt2[0] . '-' . $dt2[1] . '-01';

?>
<!doctype html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <style>
        #loading {
            position: fixed;
            left: 0px;
            top: 0px;
            width: 100%;
            height: 100vh;
            z-index: 100;
            opacity: 0.3;
            background: url(../img/oscar.gif) 50% no-repeat #fff;
        }


        table.dataTable td {
            font-size: 1em;
        }

        table.dataTable tr.dtrg-level-0 td {
            font-size: 0.9em;
        }

        table.dataTable th {
            font-size: 0.9em;
        }

        th,
        td {
            height: 3px;
        }
    </style>
    <link rel="canonical" href="https://keenthemes.com/metronic" />
    <!--begin::Fonts-->
    <!-- <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Poppins:300,400,500,600,700" />
    end::Fonts -->
    <!--begin::Global Theme Styles(used by all pages)-->
    <link href="../assets/plugins/global/plugins.bundle.css" rel="stylesheet" type="text/css" />
    <link href="../assets/plugins/custom/prismjs/prismjs.bundle.css" rel="stylesheet" type="text/css" />
    <!-- <link href="../assets/css/style.bundle.css" rel="stylesheet" type="text/css" /> -->
    <!--end::Global Theme Styles-->
    <!--begin::Layout Themes(used by all pages)-->
    <!--end::Layout Themes-->
    <link rel="shortcut icon" href="../assets/media/logos/favicon.ico" />

    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <script
        src="https://code.jquery.com/jquery-3.7.1.js"
        integrity="sha256-eKhayi8LEQwp4NKxN+CfCh+3qOVUtJn3QNZ0TciWLP4="
        crossorigin="anonymous"></script>
    <!-- js versi 4 -->
    <!-- <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script> -->

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css" />
    <script src="https://cdn.datatables.net/1.10.19/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.1/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.1/js/dataTables.bootstrap5.min.js"></script>
    <!-- <link rel="stylesheet" href="https://cdn.datatables.net/2.1.8/css/dataTables.dataTables.min.css" /> -->
    <!-- <script src="https://cdn.datatables.net/2.1.8/js/dataTables.min.js"></script> -->
    <link rel="apple-touch-icon" sizes="76x76" href="assets/img/apple-icon.png" />
    <link rel="icon" type="image/png" href="assets/img/favicon.png" />
    <title>Micro Dashboard by Sankingdom</title>
    <!--     Fonts and icons     -->
    <link href="https://fonts.googleapis.com/css?family=Open+Sans:300,400,600,700" rel="stylesheet" />
    <!-- Nucleo Icons -->
    <link href="assets/css/nucleo-icons.css" rel="stylesheet" />
    <link href="assets/css/nucleo-svg.css" rel="stylesheet" />

    <link href="assets/css/nucleo-svg.css" rel="stylesheet" />
    <!-- CSS Files -->
    <link id="pagestyle" href="assets/css/soft-ui-dashboard.css" rel="stylesheet" />
    <!-- <link id="pagestyle" href="assets/css/corporate-ui-dashboard.css" rel="stylesheet" /> -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.3.1/dist/chart.min.js"></script>
    <style>
        html {
            font-size: 0.9rem;
        }

        .card {
            background-color: #fff;
            border-radius: 10px;
            border: none;
            position: relative;
            margin-bottom: 30px;
            box-shadow: 0 0.46875rem 2.1875rem rgba(90, 97, 105, 0.1), 0 0.9375rem 1.40625rem rgba(90, 97, 105, 0.1), 0 0.25rem 0.53125rem rgba(90, 97, 105, 0.12), 0 0.125rem 0.1875rem rgba(90, 97, 105, 0.1);
            background-position: right top;
            background-size: 30% auto;
            background-image: url("assets/media/svg/shapes/abstract-4.svg")
        }

        .l-back-red {
            background-color: #C40003 !important;
            color: #fff !important;

        }

        .l-back-green {
            background-color: #00DB26 !important;
            color: #fff !important;

        }

        .l-back-orange {
            background-color: #FFDB24 !important;
            color: #000 !important;

        }

        .l-bg-red {
            background: linear-gradient(to top, #F7A40D, #F70D60) !important;
            color: #fff;

        }

        .l-bg-blue-dark {
            background: linear-gradient(to right, #373b44, #4286f4) !important;
            color: #fff;
        }

        .l-bg-dark-dark {
            background: linear-gradient(to right, #153448, #3C5B6F) !important;
            color: #fff;
        }

        .l-bg-green-dark {
            background: linear-gradient(to top, #0a504a, #38ef7d) !important;
            color: #fff;
        }

        .l-bg-orange {
            background: linear-gradient(to right, white, yellow) !important;
            color: #fff;
        }

        .card .card-statistic-3 .card-icon-large .fas,
        .card .card-statistic-3 .card-icon-large .far,
        .card .card-statistic-3 .card-icon-large .fab,
        .card .card-statistic-3 .card-icon-large .fal {
            font-size: 120px;
        }

        .card .card-statistic-3 .card-icon {
            text-align: center;
            line-height: 0px;
            margin-left: 15px;
            color: #000;
            position: absolute;
            right: -5px;
            top: 20px;
            opacity: 0.1;
        }

        .l-bg-cyan {
            background: linear-gradient(135deg, #289cf5, #84c0ec) !important;
            color: #fff;
        }

        .l-bg-green {
            background: linear-gradient(to top, #C7E8CA, #CDFAD5) !important;
            color: #000;
        }

        .l-bg-orange {
            background: linear-gradient(to top, #F6FDC3, #FFE382) !important;
            color: #000;
        }

        .l-bg-cyan {
            background: linear-gradient(135deg, #289cf5, #84c0ec) !important;
            color: #fff;
        }

        .right {
            text-align: right;
            margin-right: 1em;
        }

        .left {
            text-align: left;
            margin-left: 1em;
        }

        .column {
            float: left;
            width: 33.33%;
        }

        /* Clear floats after the columns */
        .row:after {
            content: "";
            display: table;
            clear: both;
        }

        /* Scrolling News Ticker Styles */
        .news-ticker-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 40px;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            border-bottom: 3px solid #ffd700;
            z-index: 1050;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        }

        .news-ticker-wrapper {
            height: 100%;
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

        .news-ticker-content {
            display: flex;
            align-items: center;
            white-space: nowrap;
            animation: scroll-left 120s linear infinite;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 14px;
            font-weight: 500;
            color: #ffffff;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
        }

        .news-item {
            margin-right: 80px;
            padding: 0 20px;
            display: inline-flex;
            align-items: center;
            position: relative;
        }

        .news-item::after {
            content: "•";
            position: absolute;
            right: -40px;
            color: #ffd700;
            font-size: 16px;
            font-weight: bold;
        }

        .news-item:last-child::after {
            display: none;
        }

        @keyframes scroll-left {
            0% {
                transform: translateX(100vw);
            }

            100% {
                transform: translateX(-100%);
            }
        }

        /* Hover effect to pause animation */
        .news-ticker-container:hover .news-ticker-content {
            animation-play-state: paused;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .news-ticker-container {
                height: 35px;
            }

            .news-ticker-content {
                font-size: 12px;
                animation-duration: 100s;
            }

            .news-item {
                margin-right: 60px;
                padding: 0 15px;
            }
        }

        @media (max-width: 480px) {
            .news-ticker-container {
                height: 30px;
            }

            .news-ticker-content {
                font-size: 11px;
                animation-duration: 80s;
            }

            .news-item {
                margin-right: 40px;
                padding: 0 10px;
            }
        }

        /* Adjust body padding to accommodate fixed ticker */
        body {
            padding-top: 40px !important;
        }

        @media (max-width: 768px) {
            body {
                padding-top: 35px !important;
            }
        }

        @media (max-width: 480px) {
            body {
                padding-top: 30px !important;
            }
        }

        /* Glowing effect for special emphasis */
        .news-ticker-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            animation: shine 3s infinite;
        }

        @keyframes shine {
            0% {
                left: -100%;
            }

            100% {
                left: 100%;
            }
        }

        /* Recovery Chart Styles */
        .chart-container {
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border-radius: 8px;
            padding: 10px;
            box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .chart-container canvas {
            border-radius: 6px;
        }

        /* Enhanced table styling for recovery card */
        .recovery-table {
            font-size: 0.75rem;
        }

        .recovery-table td {
            padding: 0.25rem 0.5rem;
            vertical-align: middle;
        }

        .recovery-table .progress {
            height: 4px;
            border-radius: 2px;
            background-color: rgba(0, 0, 0, 0.1);
        }

        .recovery-table .progress-bar {
            border-radius: 2px;
        }

        /* Badge styling for recovery */
        .badge-sm {
            font-size: 0.65rem;
            padding: 0.25rem 0.5rem;
            border-radius: 0.375rem;
        }

        /* Responsive chart adjustments */
        @media (max-width: 1200px) {
            .chart-container {
                height: 160px !important;
            }
        }

        @media (max-width: 768px) {
            .chart-container {
                height: 140px !important;
                padding: 8px;
            }

            .recovery-table {
                font-size: 0.7rem;
            }
        }

        @media (max-width: 576px) {
            .chart-container {
                height: 120px !important;
                padding: 6px;
            }
        }
    </style>


    <script>
        $('.alert').alert();
        $('#myAlert').on('closed.bs.alert', function() {
            // do something
        })
    </script>

    <script>
        $(document).ready(function() {
            $(".toast").toast({
                autohide: true
            });
        });
    </script>



</head>
<?php

?>

<body class="g-sidenav-show bg-gray-100">

    <!-- Scrolling News Ticker -->
    <div class="news-ticker-container">
        <div class="news-ticker-wrapper">
            <div class="news-ticker-content">
                <span class="news-item">📢 Selamat datang di Dashboard Regional Mikro BRI - Wilayah <?php echo strtoupper($kode_wilayah); ?></span>
                <span class="news-item">💼 Pantau kinerja realisasi pinjaman secara real-time periode <?php echo date('F Y', strtotime($tgl_max)); ?></span>
                <span class="news-item">📊 Data terbaru: L1133 (<?php echo date('d/m/Y', strtotime($tgl_max)); ?>), Neraca (<?php echo date('d/m/Y', strtotime($tgl_max_neraca)); ?>), LW321 (<?php echo date('d/m/Y', strtotime($tgl_max_lw321)); ?>)</span>
                <span class="news-item">🎯 Target pencapaian guidance dan monitoring realisasi harian</span>
                <span class="news-item">📈 Analisis trend perkembangan portfolio dan perbandingan periode</span>
                <span class="news-item">⚡ Update data otomatis dari sistem BRISIM setiap hari kerja</span>
                <span class="news-item">🔔 Sistem notifikasi otomatis untuk data yang belum terupdate</span>
                <span class="news-item">📱 Dashboard responsif - akses dari desktop, tablet, atau smartphone</span>
                <span class="news-item">🏆 Monitoring pencapaian target bulanan dan tahunan</span>
                <span class="news-item">📋 Laporan komprehensif untuk analisis manajemen</span>
                <span class="news-item">🔐 Akses aman dengan sistem role-based authentication</span>
                <span class="news-item">⏰ Waktu akses: <?php echo date('d F Y, H:i:s'); ?> WIB</span>
            </div>
        </div>
    </div>

    <!-- Modal data belum update-->
    <?php
    // echo $_SESSION['notif'];

    if ($show_modal_update && $_SESSION['notif'] < 1):
        $_SESSION['notif']++;
    ?>
        <div class="modal fade" id="warningModal" tabindex="-1" role="dialog" aria-labelledby="warningModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered" role="document">
                <div class="modal-content">
                    <div class="modal-header bg-light-info text-dark">
                        <h5 class="modal-title" id="warningModalLabel"><?= $judul_modal ?></h5>
                    </div>
                    <div class="modal-body">
                        <?= $isi_modal ?>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-light" data-bs-dismiss="modal">Tutup</button>
                    </div>
                </div>
            </div>
        </div>

        <script>
            $(document).ready(function() {
                $('#warningModal').modal('show');
            });
        </script>
    <?php endif; ?>

    <?php
    if ($_SESSION['visit'] == '') {
        $_SESSION['visit'] = $_SESSION['visit'] + 1;
    ?>




    <?php
    }
    ?>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            $('.alert').alert();

            // Enhanced News Ticker Functionality
            const newsTickerContent = document.querySelector('.news-ticker-content');
            const newsContainer = document.querySelector('.news-ticker-container');

            if (newsTickerContent && newsContainer) {
                // Add click event to pause/resume animation
                newsContainer.addEventListener('click', function() {
                    const currentState = newsTickerContent.style.animationPlayState;
                    if (currentState === 'paused') {
                        newsTickerContent.style.animationPlayState = 'running';
                        newsContainer.style.cursor = 'pointer';
                    } else {
                        newsTickerContent.style.animationPlayState = 'paused';
                        newsContainer.style.cursor = 'pointer';
                    }
                });

                // Add smooth transition effects
                newsContainer.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.02)';
                    this.style.transition = 'transform 0.3s ease';
                });

                newsContainer.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1)';
                });

                // Dynamic speed adjustment based on content length
                const contentWidth = newsTickerContent.scrollWidth;
                const containerWidth = newsContainer.offsetWidth;
                const baseSpeed = 120; // base animation duration in seconds
                const adjustedSpeed = Math.max(60, (contentWidth / containerWidth) * 30);
                newsTickerContent.style.animationDuration = adjustedSpeed + 's';

                // Add accessibility
                newsContainer.setAttribute('role', 'marquee');
                newsContainer.setAttribute('aria-label', 'Scrolling news ticker');
                newsContainer.setAttribute('title', 'Klik untuk pause/resume, hover untuk memperlambat');
            }
        });
    </script>

    <div id="loading"></div>

    <!--   Core JS Files   -->
    <script src="assets/js/core/popper.min.js"></script>
    <script src="assets/js/core/bootstrap.min.js"></script>

    <!-- Plugin for the charts, full documentation here: https://www.chartjs.org/ -->
    <script src="assets/js/plugins/chartjs.min.js"></script>
    <script src="assets/js/plugins/Chart.extension.js"></script>

    <!-- Control Center for Soft Dashboard: parallax effects, scripts for the example pages etc -->

    <script src="assets/js/soft-ui-dashboard.min.js"></script>
    <br>
    <?php
    // print_r($_POST);
    if ($_GET['menu'] == '') {
        if ($_POST['pilih-unit'] == '') {
            $branch = $_POST['pilih-uker'];
        } else {
            $branch = $_POST['pilih-unit'];
        }

        if ($_SESSION['role'] == 'super354') {
            $branch = $_SESSION['kodeuker'];
        }
        if ($_POST['pilih-uker'] != '') {
            $branch = $_POST['pilih-uker'];
        }
        if ($_POST['pilih-unit'] != '') {
            $branch = $_POST['pilih-unit'];
        }

        if ($_SESSION['role'] == 'super354') {
            $_POST['pilih-unit'] = $_POST['pilih-uker'];
        }

        if ($_SESSION['role'] == 'reg') {
            $_POST['pilih-unit'] = $_SESSION['kodeuker'];
            $_POST['pilih-uker'] = $_SESSION['kodeuker'];
            $branch = $_POST['pilih-unit'];
            $branch = $_POST['pilih-uker'];
        }

        if ($_SESSION['role'] == 'area') {
            $_POST['pilih-area'] = $_SESSION['kodeuker'];
        }

        if ($_POST['pilih-area'] != '') {

            if ($_POST['pilih-uker'] == '') {
                $branch = $_POST['pilih-area'];
            } else if ($_POST['pilih-uker'] != '' && $_POST['pilih-unit'] == '') {
                $branch = $_POST['pilih-uker'];
            } else if ($_POST['pilih-unit'] != '') {
                $branch = $_POST['pilih-unit'];
            }
        }

        // print_r($_SESSION);
        // print_r($_POST);



        $unit = get_list_unit($_POST['pilih-uker']);
        // color rka

        // echo $branch;

        // $txt[0] = alert_msg_nopn($branch, $datedprev);

        // $mantri_no_real = get_mantri_blm_real($branch, $tgl_max);

        // $txt[1] = 'Terdapat <b>Mantri Belum `Realisasi`</b> sejak awal bulan sebanyak :  <b>' . $mantri_no_real . '</b>   Orang <br> Klik untuk <a href="?mode=mantri_blm_real"><b>detail</b></a>';

        // $array_rand = rand(0, (count($txt) - 1));  // print_r($txt);

        // $_SESSION['show']++;

    ?>

        <div class="container-fluid">
            <?php
            if ($_SESSION['show'] % 2 == 0 && ($_SESSION['role'] == 'super354' || $_SESSION['role'] == 'god')) {
                //   $_SESSION['show']++;
            ?>



                <!-- <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <div>

                        <strong>Perhatian!</strong>
                        <div class="text-white"><?php echo $txt[$array_rand]; ?> <a type="button" class="btn-close" data-dismiss="alert" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </a></div>

                    </div>

                </div> -->
            <?php } ?>
            <h3><a href="index.php">Highlight Kinerja</a> </h3>
            <form action="" method="post">
                <div class="row">
                    <?php if ($_SESSION['role'] != 'reg') { ?>
                        <div class="col-sm">
                            <?php if ($_SESSION['role'] == 'god') { ?>
                                <label>Micro Business Area : </label>

                                <select class="form-control" name="pilih-area" id="pilih_uker_area" onchange="this.form.submit()">
                                    <option value="">All Area </option>
                                    <?php
                                    $sql_area = "select * from uker where uker_type = 'area' and region = '$kode_wilayah'";
                                    $rs_area = mysqli_query($koneksi, $sql_area);
                                    while ($dat_area = mysqli_fetch_array($rs_area)) {
                                        if ($dat_area['kode_uker'] == $_POST['pilih-area']) {
                                            $str = 'selected';
                                        } else {
                                            $str = '';
                                        }
                                    ?>
                                        <option value="<?php echo $dat_area['kode_uker'] ?>" <?php echo $str; ?>><?php echo $dat_area['nama_uker']; ?></option>
                                    <?php } ?>
                                </select>
                            <?php } ?>
                            <label>Branch Office</label>
                            <select class="form-control" name="pilih-uker" id="pilih_uker" onchange="this.form.submit()">
                                <option value="">-- All Branch Office --</option>
                                <?php

                                $uker = get_uker_region_kc($kode_wilayah);

                                if (($_SESSION['role'] == 'super354')) {
                                    $uker = get_list_unit($_SESSION['kodeuker']);
                                } else if ($_SESSION['role'] == 'god' && $_POST['pilih-area'] == '') {
                                    $uker = get_uker_region_kc($kode_wilayah);
                                } else if ($_SESSION['role'] == 'god' && $_POST['pilih-area'] != '') {
                                    $uker = get_uker_region_kc($_POST['pilih-area']);
                                } else if ($_SESSION['role'] == 'area') {
                                    $uker = get_uker_region_kc($_SESSION['kodeuker']);
                                }

                                while ($dat_u = mysqli_fetch_array($uker)) {
                                    if (($dat_u['kode_uker'] == $_POST['pilih-uker']) || $dat_u['kode_uker'] == $_GET['pilih-uker']) {
                                        $s = 'selected';
                                    } else {
                                        $s = '';
                                    }
                                ?>
                                    <option value="<?php echo $dat_u['kode_uker'] ?>" <?php echo $s; ?>><?php echo $dat_u['nama_uker']; ?></option>
                                <?php } ?>

                            </select>
                        </div>
                    <?php } ?>


                    <?php if ($_SESSION['role'] == 'god' || $_SESSION['role'] == 'area' && $_GET['mode'] == '') { ?>
                        <div class="col-sm">
                            <label>Unit</label>
                            <select class="form-control" name="pilih-unit" id="pilih_unit" onchange="this.form.submit()">
                                <option value="">-- All Unit --</option>
                                <?php
                                if (($_POST['pilih-uker'] != '')) {
                                    while ($units = mysqli_fetch_array($unit)) {
                                        if ($units['kode_uker'] == $_POST['pilih-unit']) {
                                            $s = 'selected';
                                            $adaygdipilih = '1';
                                        } else {
                                            $s = '';
                                        }
                                ?>
                                        <option value="<?php echo $units['kode_uker'] ?>" <?php echo $s; ?>><?php echo $units['nama_uker'] ?></option>
                                <?php }
                                } ?>

                            </select>

                        </div>
                    <?php } ?>

                    <div class="col-sm">
                        <label>Pilih Periode</label>
                        <select class="form-control" name="pilih_periode" id="pilih_uker" onchange="this.form.submit()">
                            <?php while ($dat_tgl = mysqli_fetch_array($rs_list_date)) {
                                if ($_POST['pilih_periode'] == $dat_tgl[0]) {
                                    $sel = 'selected';
                                } else {
                                    $sel = '';
                                } ?>
                                <option value="<?php echo $dat_tgl[0]; ?>" <?php echo $sel; ?>><?php echo tgl_periode($dat_tgl[0]); ?>
                                <?php } ?>
                        </select>
                    </div>
                </div>
            </form>
            <p>

                <?php

                if ($_POST['pilih-uker'] == '' && $_POST['pilih-area'] == '') {
                    if ($_SESSION['kodeuker'] == $kode_wilayah) {
                        $uker_name = get_nama_uker($kode_wilayah, $koneksi);
                    } else {
                        $uker_name = get_nama_uker($_SESSION['kodeuker'], $koneksi);
                    }
                } else {
                    if ($_POST['pilih-uker'] != '') {
                        $uker_name = get_nama_uker($_POST['pilih-uker'], $koneksi);
                    } else {
                        $uker_name = get_nama_uker($_POST['pilih-area'], $koneksi);
                    }
                }

                if ($_POST['pilih-unit'] != '' && $adaygdipilih == '1') {
                    $uker_name_unit = get_nama_uker($_POST['pilih-unit'], $koneksi);
                } else {
                    $uker_name_unit = '';
                }

                ?>
            <h4 class="text-center"> <b><?php echo $uker_name; ?> <?php if ($uker_name_unit != '') {
                                                                        echo ' - ' . $uker_name_unit;
                                                                    } ?></b> </h4>
            <h6 class="text-center"> Periode : <?php echo tgl_periode($tgl_max); ?> </h6>
            </p>
            <?php
            if ($_GET['mode'] != '') {
                include 'map_mode.hp.php';
            } else {
            ?>
                <!-----  statistic card --->

                <?php


                // die('halt');
                $stmt = $mysqli->prepare('select region from uker where kode_uker = ? and uker_type = ?');
                $stmt->bind_param('ss', $kode_uker, $level_uker);
                $kode_uker = $_SESSION['kodeuker'];
                $level_uker = cek_branch2($_SESSION['kodeuker']);
                $stmt->execute();
                $rs_cek = $stmt->get_result();
                $dat_cek = $rs_cek->fetch_assoc();

                $dat_os = array();
                if (empty($branch)) {
                    $branch = $kode_wilayah;
                }

                $dat_os = get_all_os($tgl_max, $branch);

                $dat_os_prev = get_all_os($datedprev, $branch);
                $dat_os_pyear = get_all_os($prevyear, $branch);
                $dat_os_day = get_all_os($lastday, $branch);
                $dat_os_mom = get_all_os($mom_date, $branch);

                $mtd_os = $dat_os - $dat_os_prev;
                $ytd_os = $dat_os - $dat_os_pyear;
                $dtd_os = $dat_os - $dat_os_day;
                $os_mom = $dat_os - $dat_os_mom;

                $total_os = $dat_os / $satuan;

                $mtd_os = $mtd_os / $satuan;
                $ytd_os = $ytd_os / $satuan;
                $dtd_os = $dtd_os / $satuan;
                $os_mom = $os_mom / $satuan;
                $rka_os = get_rka('113', $date_rka, $branch);
                $rka_os = $rka_os / $satuan;
                // $persen_os = ($total_os / $rka_os) * 100;
                if ($rka_os != 0) {
                    $persen_os = ($total_os / $rka_os) * 100;
                } else {
                    $persen_os = 0;
                }

                $gap_os = $total_os - $rka_os;


                $col_os = get_color($mtd_os);
                $col_osy = get_color($ytd_os);
                $col_osd = get_color($dtd_os);
                $col_mom_os = get_color($os_mom);

                $btn1 = '<span class="fs-5"><b>Rp. ' . number_format($total_os) . '</b></span><br> DTD : ' . '<i class="ni ni-bold-' . $col_osd['way'] . ' text-' . $col_osd['color'] . '" aria-hidden="true"></i>' . number_format($dtd_os) . ' <br> MTD: ' . '<i class="ni ni-bold-' . $col_os['way'] . ' text-' . $col_os['color'] . '" aria-hidden="true"></i>' . number_format($mtd_os) . ' <br> YTD: ' . '<i class="ni ni-bold-' . $col_osy['way'] . ' text-' . $col_osy['color'] . '"></i>' . number_format($ytd_os) . ' <br> RKA 2 : ' . number_format($rka_os) . '  (' . number_format($persen_os, 2) . '% )';

                // $dat_is = get_all_is($tgl_max);
                // $delta_is = get_mtd_is_all($tgl_max);
                $kode_wilayah = getRegion($_SESSION['kodeuker']);

                $total_dana = get_all_is($tgl_max, $branch);
                // echo $total_dana;
                $prev_dana = get_all_is($datedprev, $branch);
                $prev_year_dana = get_all_is($prevyear, $branch);
                $prev_day_dana = get_all_is($lastday, $branch);
                $mom_dana = get_all_is($mom_date, $branch);
                // echo $prev_year_dana;
                $mom_dana = $total_dana - $mom_dana;
                $mtd_dana = $total_dana - $prev_dana;
                $ytd_dana = $total_dana - $prev_year_dana;
                $dtd_dana = $total_dana - $prev_day_dana;
                $total_dana = $total_dana / $satuan;
                $rka_is = get_rka('25', $date_rka, $branch) / $satuan;
                // $persen_rka_is = ($total_dana / $rka_is) * 100;
                if ($rka_is != 0) {
                    $persen_rka_is = ($total_dana / $rka_is) * 100;
                } else {
                    $persen_rka_is = 0;
                }
                $ytd_dana = $ytd_dana / $satuan;
                $dtd_dana = $dtd_dana / $satuan;
                $mom_dana = $mom_dana / $satuan;
                // $ldr = ($total_os / $total_dana) * 100;
                if ($total_dana != 0) {
                    $ldr = ($total_os / $total_dana) * 100;
                } else {
                    $ldr = 0;
                }
                $mtd_dana = $mtd_dana / $satuan;
                // $mtd_dana = ($total_dana - $total_dana_p);

                $dpk = get_os_dpk($tgl_max, $branch);
                $os_dpk = $dpk['os'] / $satuan;
                $deb_dpk = $dpk['deb'];

                // $persen_dpk = ($os_dpk / $total_os) * 100;
                if ($total_os != 0) {
                    $persen_dpk = ($os_dpk / $total_os) * 100;
                } else {
                    $persen_dpk = 0;
                }

                $dpk_prev_day = get_os_dpk($lastday, $branch);
                $dtd_dpk = $dpk['os'] - $dpk_prev_day['os'];
                $dtd_dpk = $dtd_dpk / $satuan;
                $dpk_prev = get_os_dpk($datedprev, $branch);
                $os_dpk_prev = $dpk_prev['os'] / $satuan;
                $mtd_dpk = $os_dpk - $os_dpk_prev;
                $dpk_prev_year = get_os_dpk($prevyear, $branch);
                $os_dpk_prev_year = $dpk_prev_year['os'] / $satuan;
                $os_dpk_mom = get_os_dpk($mom_date, $branch);
                $mom_dpk = $os_dpk - ($os_dpk_mom['os'] / $satuan);

                // echo $os_dpk_prev_year;
                $ytd_dpk = $os_dpk - $os_dpk_prev_year;
                $rka_dpk = get_rka('114', $date_rka, $branch) / $satuan;
                $persen_rka_dpk = ($rka_dpk / $os_dpk) * 100;

                $npl = get_os_npl($tgl_max, $branch);
                $os_npl = $npl['os'] / $satuan;
                $deb_npl = $npl['deb'];

                // $persen_npl = ($os_npl / $total_os) * 100;
                if ($total_os != 0) {
                    $persen_npl = ($os_npl / $total_os) * 100;
                } else {
                    $persen_npl = 0;
                }

                $npl_prev = get_os_npl($datedprev, $branch);
                $npl_prev_day = get_os_npl($lastday, $branch);

                $os_npl_prev = $npl_prev['os'] / $satuan;
                $mtd_npl = $os_npl - $os_npl_prev;
                $npl_prev_year = get_os_npl($prevyear, $branch);
                $os_npl_prevyear = $npl_prev_year['os'] / $satuan;
                $os_npl_mom = get_os_npl($mom_date, $branch);
                $mom_npl = $os_npl - ($os_npl_mom['os'] / $satuan);

                $ytd_npl = $os_npl - $os_npl_prevyear;
                $dtd_npl = $npl['os'] - $npl_prev_day['os'];
                $dtd_npl = $dtd_npl / $satuan;
                $rka_npl = get_rka('116', $date_rka, $branch) / $satuan;
                // echo $os_npl;
                $persen_rka_npl = ($rka_npl / $os_npl) * 100;

                $col = get_color($mtd_dana);
                $col_ytd = get_color($ytd_dana);
                $col_dtd = get_color($dtd_dana);
                $col_mom_dana = get_color($mom_dana);
                $btn2 = '  <span class="fs-5"><b> Rp. ' . number_format($total_dana) . ' </b></span><br> DTD: ' . '<i class="ni ni-bold-' . $col_dtd['way'] . ' text-' . $col_dtd['color'] . '" aria-hidden="true"></i>' . number_format($dtd_dana) . ' <br> MTD: ' . '<i class="ni ni-bold-' . $col['way'] . ' text-' . $col['color'] . '" aria-hidden="true"></i>' . number_format($mtd_dana) . ' <br> YTD: ' . '<i class="ni ni-bold-' . $col_ytd['way'] . ' text-' . $col_ytd['color'] . '"></i>' . number_format($ytd_dana) . ' <br> RKA : ' . number_format($rka_is) . '  (' . number_format($persen_rka_is, 2) . '%)';

                $rdh = get_pemasukan_dh($tgl_max, $branch);
                $pokok_dh = get_dh_pokok($tgl_max, $branch);
                $klaim_dh = get_dh_klaim($tgl_max, $branch);

                $rdh_prev = get_pemasukan_dh($datedprev, $branch);
                $rdh_pyear = get_pemasukan_dh($prevyear, $branch);
                $rdh_lastday = get_pemasukan_dh($lastday, $branch);

                $ph = get_ph($branch, $datedprev);
                // $ph_prev = get_ph($branch, $datedprev);3

                $mtd_rdh = $rdh - $rdh_prev;
                $dtd_rdh = $rdh - $rdh_lastday;
                $ytd_rdh = $rdh - $rdh_pyear;

                $rka_dh = get_rka('139', $date_rka, $branch);
                // $persentase_dh = ($rdh / $rka_dh) * 100;
                if ($rka_dh != 0) {
                    $persentase_dh = ($rdh / $rka_dh) * 100;
                } else {
                    $persentase_dh = 0;
                }


                // laba rugi
                // $laba_rugi = get_laba_rugi($tgl_max, $branch);
                $laba_rugi_prev = get_laba_rugi($datedprev, $branch);
                $rka_laba_rugi = get_rka('135', $date_rka_prev, $branch);
                // $persen_rka_lr = ($laba_rugi_prev / $rka_laba_rugi) * 100;
                if ($rka_laba_rugi != 0) {
                    $persen_rka_lr = ($laba_rugi_prev / $rka_laba_rugi) * 100;
                } else {
                    $persen_rka_lr = 0;
                }

                $sql_max_periode = 'select periode from lw321 order by periode desc limit 1';
                $rs_max_p = mysqli_query($koneksi, $sql_max_periode);
                $dat_max_p = mysqli_fetch_array($rs_max_p);

                if ($_POST['pilih_periode'] != '') {
                    $dat_max_p['periode'] = $_POST['pilih_periode'];
                } else {
                    $sql_max_periode = 'select periode from lw321 order by periode desc limit 1';
                    $rs_max_p = mysqli_query($koneksi, $sql_max_periode);
                    $dat_max_p = mysqli_fetch_array($rs_max_p);
                }

                // override
                // $dat_max_p['periode'] = '2024-10-31';

                $lancar_restruk = get_lancar_restruk($dat_max_p['periode'], $branch);
                $lancar_restruk_prev = get_lancar_restruk($datedprev, $branch);
                $rka_lancar_restruk = get_rka('7064', $date_rka, $branch);
                // $persen_lancar_restruk = ($rka_lancar_restruk / $lancar_restruk) * 100;
                if ($lancar_restruk != 0) {
                    $persen_lancar_restruk = ($rka_lancar_restruk / $lancar_restruk) * 100;
                } else {
                    $persen_lancar_restruk = 0;
                }
                include 'stat-bar.php';
                ?>
                <div class="row">
                    <div class="col-xl-3 col-sm-6 mb-xl-0 mb-4">
                        <div class="card">
                            <div class="card-body p-3">
                                <div class="row">
                                    <div class="col-8">
                                        <div class="numbers">
                                            <p class="text-sm mb-0 text-capitalize font-weight-bold">
                                            <h5>Total Loan</h5>
                                            </p>
                                            <h5 class="font-weight-bolder mb-0">
                                                <?php
                                                echo '<span class="fs-3"><b>' . number_format($total_os) . '</b></span>';
                                                ?></h5>
                                            <p class="text-sm mb-0 text-capitalize font-weight-bold">- </p>
                                            <?php if ($_POST['pilih-unit'] == '') { ?>
                                                <a href="?mode=loan&pilih-uker=<?php echo $_POST['pilih-uker'] ?>&pilih-area=<?php echo $_POST['pilih-area'] ?>" class="stretched-link"></a>
                                            <?php } ?>
                                            <table class="table">
                                                <tr class="text-sm mb-0 fonAt-weight-bold">
                                                    <td nowrap>DTD : <i class="ni ni-bold-<?php echo $col_osd['way']; ?> text-<?php echo $col_osd['color'] ?>" aria-hidden="true"> <?php echo number_format($dtd_os) ?></i></td>
                                                    <td nowrap> MTD : <i class="ni ni-bold-<?php echo $col_os['way']; ?> text-<?php echo $col_os['color'] ?>"> <?php echo number_format($mtd_os) ?></i></td>
                                                </tr>
                                                <tr class="text-sm mb-0 font-weight-bold">

                                                    <td nowrap> MoM : <i class="ni ni-bold-<?php echo $col_mom_os['way']; ?> text-<?php echo $col_mom_os['color'] ?>"> <?php echo number_format($os_mom) ?></i></td>
                                                    <td nowrap> YTD : <i class="ni ni-bold-<?php echo $col_osy['way']; ?> text-<?php echo $col_osy['color'] ?>"> <?php echo number_format($ytd_os) ?></i> </td>
                                                </tr>
                                                <tr class="text-sm mb-0 font-weight-bold">
                                                    <td> RKA : <?php echo number_format($rka_os) ?></td>
                                                    <td> <span class="badge rounded-pill text-bg-<?php echo get_color_scale2($persen_os) ?>"> <?php echo number_format($persen_os, 2) . '%'; ?> </span></td>
                                                </tr>
                                                <tr class="text-sm mb-0 font-weight-bold">
                                                    <td> GAP RKA : <?php echo number_format($gap_os); ?></td>
                                                    <td> </td>
                                                </tr>
                                            </table>
                                        </div>
                                    </div>
                                    <div class="col-4 text-end">
                                        <div class="icon icon-shape bg-gradient-<?php echo get_color_scale2($persen_os) ?> shadow text-center border-radius-md">
                                            <i class="ni ni-archive-2"></i>
                                        </div>

                                    </div>
                                    <?php
                                    // echo 'variabel $kode_wilayah :' . $kode_wilayah = getRegion($_SESSION['kodeuker']);
                                    $arr = get_history_os($branch);  // print_r($arr);
                                    ?>
                                    <div id="chart"></div>
                                    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
                                    <script>
                                        <?php

                                        $graph1 = implode(',', $arr);
                                        $dates_m = getLast15Days($tgl_max);
                                        $dates_m = formatDates($dates_m);

                                        for ($month = 0; $month <= count($dates_m); $month++) {
                                            $mnt[$month] = "'" . $dates_m[$month] . "'";
                                        }

                                        $m = implode(',', $mnt);

                                        ?>
                                        document.addEventListener("DOMContentLoaded", function() {

                                            var options = {

                                                chart: {

                                                    type: 'area'

                                                },
                                                markers: {

                                                    size: 0,
                                                    shape: "circle"
                                                },
                                                dataLabels: {
                                                    enabled: true,
                                                    enabledOnseries: [1],
                                                    formatter: function(value) {
                                                        return new Intl.NumberFormat().format(value);
                                                    }
                                                },

                                                stroke: {
                                                    curve: 'smooth',
                                                },
                                                series: [{

                                                    name: 'os',

                                                    data: [<?php echo $graph1; ?>],
                                                    formatter: function(value) {
                                                        return new Intl.NumberFormat().format(value)
                                                    },

                                                }],

                                                xaxis: {


                                                    categories: [<?php echo $m; ?>]

                                                }

                                            };

                                            var chart = new ApexCharts(document.querySelector("#chart"), options);

                                            chart.render();

                                        });
                                    </script>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-sm-6 mb-xl-0 mb-4">
                        <div class="card">
                            <div class="card-body p-3">
                                <div class="row">
                                    <div class="col-8">
                                        <div class="numbers">
                                            <p class="text-sm mb-0 text-capitalize font-weight-bold">
                                            <h5>Dana Pihak Ketiga</h5>
                                            </p>
                                            <h5 class="font-weight-bolder mb-0">
                                                <?php
                                                $btn2 = '  <span class="fs-5"><b> Rp. ' . number_format($total_dana) . ' </b></span><br> DTD: ' . '<i class="ni ni-bold-' . $col_dtd['way'] . ' text-' . $col_dtd['color'] . '" aria-hidden="true"></i>' . number_format($dtd_dana) . ' <br> MTD: ' . '<i class="ni ni-bold-' . $col['way'] . ' text-' . $col['color'] . '" aria-hidden="true"></i>' . number_format($mtd_dana) . ' <br> YTD: ' . '<i class="ni ni-bold-' . $col_ytd['way'] . ' text-' . $col_ytd['color'] . '"></i>' . number_format($ytd_dana) . ' <br> RKA : ' . number_format($rka_is) . '  (' . number_format($persen_rka_is, 2) . '%)';

                                                echo '<span class="fs-3"><b>' . number_format($total_dana) . '</b></span>';
                                                $btn1 = '  <span class="fs-5"><b>Rp. ' . number_format($total_os) . '</b></span><br> <b>DTD :</b> ' . '<i class="ni ni-bold-' . $col_dtd['way'] . ' text-' . $col_dtd['color'] . '" aria-hidden="true"></i>' . number_format($dtd_dana) . ' <br> MTD: ' . '<i class="ni ni-bold-' . $col['way'] . ' text-' . $col['color'] . '" aria-hidden="true"></i>' . number_format($mtd_dana) . ' <br> YTD: ' . '<i class="ni ni-bold-' . $col_ytd['way'] . ' text-' . $col_ytd['color'] . '"></i>' . number_format($ytd_dana) . ' <br> RKA : ' . number_format($rka_is) . '  (' . number_format($persen_rka_is, 2) . '% )';
                                                ?></h5>
                                            <p class="text-sm mb-0 text-capitalize font-weight-bold"> - </p>
                                            <?php if ($_POST['pilih-unit'] == '') { ?>
                                                <a href="?mode=saving&pilih-uker=<?php echo $_POST['pilih-uker'] ?>" class="stretched-link"></a>
                                            <?php } ?>
                                            <table class="table">
                                                <tr class="text-sm mb-0 font-weight-bold">
                                                    <td nowrap>DTD : <i class="ni ni-bold-<?php echo $col_dtd['way']; ?> text-<?php echo $col_dtd['color'] ?>"> <?php echo number_format($dtd_dana) ?></i></td>
                                                    <td nowrap> MTD : <i class="ni ni-bold-<?php echo $col['way']; ?> text-<?php echo $col['color'] ?>"> <?php echo number_format($mtd_dana) ?></i></td>
                                                </tr>
                                                <tr class="text-sm mb-0 font-weight-bold">

                                                    <td nowrap> MoM : <i class="ni ni-bold-<?php echo $col_mom_dana['way']; ?> text-<?php echo $col_mom_dana['color'] ?>"> <?php echo number_format($mom_dana) ?></i></td>
                                                    <td nowrap> YTD : <i class="ni ni-bold-<?php echo $col_ytd['way']; ?> text-<?php echo $col_ytd['color'] ?>"> <?php echo number_format($ytd_dana) ?></i></td>
                                                </tr>

                                                <tr class="text-sm mb-0 font-weight-bold">
                                                    <td> RKA : <?php echo number_format($rka_is) ?></td>
                                                    <td> <span class="badge rounded-pill text-bg-<?php echo get_color_scale2($persen_rka_is) ?>"> <?php echo number_format($persen_rka_is, 2) . '%'; ?></span></td>
                                                </tr>
                                                <tr class="text-sm mb-0 font-weight-bold">
                                                    <td> GAP RKA : <?php
                                                                    $gap_is = $total_dana - $rka_is;
                                                                    echo number_format($gap_is); ?></td>
                                                    <td> </td>
                                                </tr>
                                            </table>
                                        </div>
                                    </div>
                                    <div class="col-4 text-end">
                                        <div class="icon icon-shape bg-gradient-<?php echo get_color_scale2($persen_rka_is) ?> shadow text-center border-radius-md">
                                            <i class="ni ni-money-coins text-lg opacity-10" aria-hidden="true"></i>
                                        </div>
                                    </div>
                                    <?php
                                    $arr_is = get_history_is($branch);
                                    // print_r($arr_is);
                                    ?>
                                    <div id="chart2"></div>
                                    <script>
                                        <?php

                                        //  $arr = get_history_os($branch);
                                        $graph2 = implode(',', $arr_is);
                                        $dates_m = getLast15Days($tgl_max);
                                        $dates_m = formatDates($dates_m);

                                        for ($month = 0; $month <= count($dates_m); $month++) {
                                            $mnt[$month] = "'" . $dates_m[$month] . "'";
                                        }

                                        $m = implode(',', $mnt);

                                        ?>
                                        document.addEventListener("DOMContentLoaded", function() {

                                            var options = {

                                                chart: {

                                                    type: 'area'

                                                },


                                                stroke: {
                                                    curve: 'smooth',
                                                },
                                                dataLabels: {
                                                    enabled: true,
                                                    enabledOnseries: [1],
                                                    formatter: function(value) {
                                                        return new Intl.NumberFormat().format(value);
                                                    }
                                                },
                                                markers: {

                                                    size: 0,
                                                    shape: "circle"
                                                },

                                                series: [{

                                                    name: 'os',

                                                    data: [<?php echo $graph2; ?>]

                                                }],

                                                xaxis: {

                                                    categories: [<?php echo $m; ?>]

                                                }

                                            };

                                            var chart = new ApexCharts(document.querySelector("#chart2"), options);

                                            chart.render();

                                        });
                                    </script>
                                </div>
                            </div>
                        </div>
                    </div>


                    <div class="col-xl-3 col-sm-6 mb-xl-0 mb-4">
                        <div class="card">
                            <div class="card-body p-3">
                                <div class="row">
                                    <div class="col-8">
                                        <div class="numbers">
                                            <p class="text-sm mb-0 text-capitalize font-weight-bold">
                                            <h5>SML</h5>
                                            </p>
                                            <h5 class="font-weight-bolder mb-0">
                                                <?php
                                                $col_dpk = get_color2($mtd_dpk);
                                                $col1_dpk = get_color2($ytd_dpk);
                                                $col2_dpk = get_color2($dtd_dpk);
                                                $col_mom_dpk = get_color2($mom_dpk);
                                                echo '<span class="fs-3"><b>' . number_format($os_dpk) . '</b></span>';
                                                ?></h5>
                                            <p class="text-sm mb-0 text-capitalize font-weight-bold"> <?php echo number_format($persen_dpk, 2) ?> % | <?php echo number_format($deb_dpk); ?> deb</p>
                                            <?php if ($_POST['pilih-unit'] == '') { ?>
                                                <a href="?mode=dpk&pilih-uker=<?php echo $_POST['pilih-uker'] ?>" class="stretched-link"></a>
                                            <?php } ?>
                                            <table class="table">
                                                <tr class="text-sm mb-0 font-weight-bold">
                                                    <td nowrap>DTD : <i class="ni ni-bold-<?php echo $col2_dpk['way']; ?> text-<?php echo $col2_dpk['color'] ?>"> <?php echo number_format($dtd_dpk) ?></i></td>
                                                    <td nowrap> MTD : <i class="ni ni-bold-<?php echo $col_dpk['way']; ?> text-<?php echo $col_dpk['color'] ?>"> <?php echo number_format($mtd_dpk) ?></i></td>
                                                </tr>
                                                <tr class="text-sm mb-0 font-weight-bold">
                                                    <td nowrap> MoM : <i class="ni ni-bold-<?php echo $col_mom_dpk['way']; ?> text-<?php echo $col_mom_dpk['color'] ?>"> <?php echo number_format($mom_dpk) ?></i></td>
                                                    <td nowrap> YTD : <i class="ni ni-bold-<?php echo $col1_dpk['way']; ?> text-<?php echo $col1_dpk['color'] ?>"> <?php echo number_format($ytd_dpk) ?></i></td>

                                                </tr>
                                                <tr class="text-sm mb-0 font-weight-bold">
                                                    <td> RKA : <?php echo number_format($rka_dpk) ?></td>
                                                    <td> <span class="badge rounded-pill text-bg-<?php echo get_color_scale2($persen_rka_dpk) ?>"> <?php echo number_format($persen_rka_dpk, 2) . '%'; ?></span></td>
                                                </tr>
                                                <tr class="text-sm mb-0 font-weight-bold">
                                                    <td> GAP RKA : <?php
                                                                    $gap_dpk = $rka_dpk - $os_dpk;
                                                                    echo number_format($gap_dpk); ?>
                                                    </td>
                                                    <td> </td>
                                                </tr>
                                            </table>

                                        </div>
                                    </div>
                                    <div class="col-4 text-end">
                                        <div class="icon icon-shape bg-gradient-<?php echo get_color_scale2($persen_rka_dpk) ?> shadow text-center border-radius-md">
                                            <i class="ni ni-ambulance text-lg opacity-10" aria-hidden="true"></i>
                                        </div>
                                    </div>
                                    <?php
                                    $arr_is = get_history_dpk($branch);
                                    // print_r($arr_is);
                                    ?>
                                    <div id="chart3"></div>
                                    <script>
                                        <?php

                                        //  $arr = get_history_os($branch);
                                        $graph2 = implode(',', $arr_is);

                                        $dates_m = getLast15Days($tgl_max);
                                        $dates_m = formatDates($dates_m);

                                        for ($month = 0; $month <= count($dates_m); $month++) {
                                            $mnt[$month] = "'" . $dates_m[$month] . "'";
                                        }

                                        $m = implode(',', $mnt);

                                        ?>
                                        document.addEventListener("DOMContentLoaded", function() {

                                            var options = {

                                                chart: {

                                                    type: 'area'

                                                },


                                                stroke: {
                                                    curve: 'smooth',
                                                },
                                                dataLabels: {
                                                    enabled: true,
                                                    enabledOnseries: [1],
                                                    formatter: function(value) {
                                                        return new Intl.NumberFormat().format(value);
                                                    }
                                                },
                                                markers: {

                                                    size: 0,
                                                    shape: "circle"
                                                },

                                                series: [{

                                                    name: 'os',

                                                    data: [<?php echo $graph2; ?>]

                                                }],

                                                xaxis: {

                                                    categories: [<?php echo $m; ?>]

                                                }

                                            };

                                            var chart = new ApexCharts(document.querySelector("#chart3"), options);

                                            chart.render();

                                        });
                                    </script>



                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-sm-6 mb-xl-0 mb-4">
                        <div class="card">
                            <div class="card-body p-3">
                                <div class="row">
                                    <div class="col-8">
                                        <div class="numbers">
                                            <p class="text-sm mb-0 text-capitalize font-weight-bold">
                                            <h5>NPL</h5>
                                            </p>
                                            <h5 class="font-weight-bolder mb-0">
                                                <?php
                                                $col_npl = get_color2($mtd_npl);
                                                $col1_npl = get_color2($ytd_npl);
                                                $col2_npl = get_color2($dtd_npl);
                                                $col_mom_npl = get_color2($mom_npl);
                                                echo '<span class="fs-3"><b>' . number_format($os_npl) . '</b></span>';
                                                ?></h5>
                                            <p class="text-sm mb-0 text-capitalize font-weight-bold"> <?php echo number_format($persen_npl, 2) ?> % | <?php echo number_format($deb_npl); ?> deb</p>
                                            <?php

                                            if (cek_branch2($_POST['pilih-unit']) != 'un') {
                                            ?>
                                                <a href="?mode=npl&pilih-uker=<?php echo $_POST['pilih-uker'] ?>" class="stretched-link"></a>
                                            <?php } ?>
                                            <table class="table">
                                                <tr class="text-sm mb-0 font-weight-bold">
                                                    <td nowrap>DTD : <i class="ni ni-bold-<?php echo $col2_npl['way']; ?> text-<?php echo $col2_npl['color'] ?>"> <?php echo number_format($dtd_npl) ?></i></td>
                                                    <td nowrap> MTD : <i class="ni ni-bold-<?php echo $col_npl['way']; ?> text-<?php echo $col_npl['color'] ?>"> <?php echo number_format($mtd_npl) ?></i></td>
                                                </tr>
                                                <tr class="text-sm mb-0 font-weight-bold">
                                                    <td nowrap> MoM : <i class="ni ni-bold-<?php echo $col_mom_npl['way']; ?> text-<?php echo $col_mom_npl['color'] ?>"> <?php echo number_format($mom_npl) ?></i></td>
                                                    <td nowrap> YTD : <i class="ni ni-bold-<?php echo $col1_npl['way']; ?> text-<?php echo $col1_npl['color'] ?>"> <?php echo number_format($ytd_npl) ?></i></td>

                                                </tr>
                                                <tr class="text-sm mb-0 font-weight-bold">
                                                    <td> RKA : <?php echo number_format($rka_npl) ?></td>
                                                    <td> <span class="badge rounded-pill text-bg-<?php echo get_color_scale2($persen_rka_npl) ?>"><?php echo number_format($persen_rka_npl, 2) . '%'; ?></span></td>
                                                </tr>
                                                <tr class="text-sm mb-0 font-weight-bold">
                                                    <td> GAP RKA : <?php
                                                                    $gap_npl = $rka_npl - $os_npl;
                                                                    echo number_format($gap_npl);
                                                                    ?></td>
                                                    <td> </td>
                                                </tr>
                                            </table>

                                        </div>
                                    </div>
                                    <div class="col-4 text-end">
                                        <div class="icon icon-shape bg-gradient-<?php echo get_color_scale2($persen_rka_npl) ?> shadow text-center border-radius-md">
                                            <i class="ni ni-sound-wave text-lg opacity-10" aria-hidden="true"></i>
                                        </div>
                                    </div>
                                    <?php
                                    $arr_npl = get_history_npl($branch);
                                    // print_r($arr_is);
                                    ?>
                                    <div id="chart4"></div>
                                    <script>
                                        <?php

                                        //  $arr = get_history_os($branch);
                                        $graph4 = implode(',', $arr_npl);
                                        $dates_m = getLast15Days($tgl_max);
                                        $dates_m = formatDates($dates_m);

                                        for ($month = 0; $month <= count($dates_m); $month++) {
                                            $mnt[$month] = "'" . $dates_m[$month] . "'";
                                        }

                                        $m = implode(',', $mnt);

                                        ?>
                                        document.addEventListener("DOMContentLoaded", function() {

                                            var options = {

                                                chart: {

                                                    type: 'area'

                                                },

                                                stroke: {
                                                    curve: 'smooth',

                                                },
                                                dataLabels: {
                                                    enabled: true,
                                                    enabledOnseries: [1],
                                                    formatter: function(value) {
                                                        return new Intl.NumberFormat().format(value);
                                                    }
                                                },
                                                markers: {

                                                    size: 0,
                                                    shape: "circle"
                                                },

                                                series: [{

                                                    name: 'os',

                                                    data: [<?php echo $graph4; ?>]

                                                }],

                                                xaxis: {

                                                    categories: [<?php echo $m; ?>]

                                                },

                                            };

                                            var chart = new ApexCharts(document.querySelector("#chart4"), options);

                                            chart.render();

                                        });
                                    </script>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
        </div>


        <div class="container-fluid py-1">
            <div class="row">
                <div class="col-xl-3 col-sm-6 mb-xl-0 mb-4">
                    <div class="card">
                        <div class="card-body p-3">
                            <div class="row">
                                <div class="col-8">
                                    <div class="numbers">
                                        <p class="text-sm mb-0 text-capitalize font-weight-bold">
                                        <h6> Lancar Restruk </h6>
                                        </p>
                                        <h5 class="font-weight-bolder mb-0">
                                            <?php
                                            $lar = ($lancar_restruk / $satuan) + $os_dpk + $os_npl;

                                            // $persen_lar = ($lar / $total_os) * 100;
                                            if ($total_os != 0) {
                                                $persen_lar = ($lar / $total_os) * 100;
                                            } else {
                                                $persen_lar = 0;
                                            }

                                            echo '<span class="fs-3"><b>' . number_format($lancar_restruk / $satuan) . '</b></span>';

                                            ?></h5>
                                        <?php if ($_POST['pilih-unit'] == '') { ?>
                                            <a href="?menu=trend-lancar-restruk&pilih-uker=<?php echo $_POST['pilih-uker'] ?>" class="stretched-link"></a>
                                        <?php } ?>
                                        <table class="table">
                                            <tr class="text-sm mb-0 font-weight-bold">
                                                <td nowrap>RKA :</td>
                                                <td nowrap><?php echo number_format($rka_lancar_restruk / $satuan, 0) ?></td>
                                                <td nowrap> <span class="badge rounded-pill text-bg-<?php echo get_color_scale2($persen_lancar_restruk) ?>"><?php echo number_format($persen_lancar_restruk, 2) ?>%</span></td>
                                            </tr>
                                            <tr class="text-sm mb-0 font-weight-bold">
                                                <td nowrap>%LAR :</td>
                                                <td nowrap><?php echo number_format($persen_lar, 2) ?>%</td>
                                                <td nowrap>-</td>
                                            </tr>
                                        </table>

                                    </div>
                                </div>
                                <div class="col-4 text-end">
                                    <div class="icon icon-shape bg-gradient-<?php echo get_color_scale2($persen_lancar_restruk) ?> shadow text-center border-radius-md">
                                        <i class="ni ni-ui-04 text-lg opacity-10" aria-hidden="true"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-sm-6 mb-xl-0 mb-4">
                    <div class="card">
                        <div class="card-body p-3">
                            <div class="row">
                                <div class="col-8">
                                    <div class="numbers">
                                        <p class="text-sm mb-0 text-capitalize font-weight-bold">
                                        <h6> LABA RUGI <?php echo tgl_periode($datedprev); ?></h6>
                                        </p>
                                        <h5 class="font-weight-bolder mb-0">
                                            <?php

                                            echo '<span class="fs-3"><b>' . number_format($laba_rugi_prev / $satuan, 0) . '</b></span>';

                                            ?></h5>
                                        <?php if ($_POST['pilih-unit'] == '') { ?>
                                            <a href="?menu=labarugi&pilih-uker=<?php echo $_POST['pilih-uker'] ?>" class="stretched-link"></a>
                                        <?php } ?>
                                        <table class="table">
                                            <tr class="text-sm mb-0 font-weight-bold">
                                                <td nowrap>RKA :</td>
                                                <td nowrap><?php echo number_format($rka_laba_rugi / $satuan, 0) ?></td>
                                                <td nowrap> <span class="badge rounded-pill text-bg-<?php echo get_color_scale2($persen_rka_lr) ?>"><?php echo number_format($persen_rka_lr, 2) ?>%</span></td>
                                            </tr>
                                            <tr class="text-sm mb-0 font-weight-bold">
                                                <td nowrap>GAP RKA :</td>
                                                <td nowrap><?php

                                                            $gap_lr = $laba_rugi_prev - $rka_laba_rugi;
                                                            echo number_format($gap_lr / $satuan, 0) ?></td>

                                            </tr>

                                        </table>
                                    </div>
                                </div>
                                <div class="col-4 text-end">
                                    <div class="icon icon-shape bg-gradient-<?php echo get_color_scale2($persen_rka_lr) ?> shadow text-center border-radius-md">
                                        <i class="ni ni-chart-bar-32 text-lg opacity-10" aria-hidden="true"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-sm-6 mb-xl-0 mb-4">
                    <div class="card">
                        <div class="card-body p-3">
                            <div class="row">
                                <div class="col-8">
                                    <div class="numbers">
                                        <p class="text-sm mb-0 text-capitalize font-weight-bold">
                                        <h6>LDR </h6>
                                        </p>
                                        <h5 class="font-weight-bolder mb-0">
                                            <?php

                                            echo '<span class="fs-3"><b>' . number_format($ldr, 2) . ' %</b></span>';

                                            ?></h5>
                                        <?php if ($_POST['pilih-unit'] == '') { ?>
                                            <a href="?mode=ldr&pilih-uker=<?php echo $_POST['pilih-uker'] ?>" class="stretched-link">-</a>
                                        <?php } ?>

                                    </div>
                                </div>
                                <div class="col-4 text-end">
                                    <div class="icon icon-shape bg-gradient-secondary ?> shadow text-center border-radius-md">
                                        <i class="ni ni-key-25 text-lg opacity-10" aria-hidden="true"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-sm-6 mb-xl-0 mb-4">
                    <div class="card">
                        <div class="card-body p-3">
                            <div class="row">
                                <div class="col-8">
                                    <div class="numbers">
                                        <p class="text-sm mb-0 text-capitalize font-weight-bold">
                                        <h6>Total Write Off PH <?php echo date('Y'); ?></h6>

                                        <h5 class="font-weight-bolder mb-0">
                                            <?php

                                            $dat_ph = get_ph_all($branch, date('Y'));
                                            echo '<span class="fs-3"><b>' . number_format($dat_ph / $satuan, 0) . ' </b></span><br>';

                                            $last_ph = get_ph($branch, $datedprev);

                                            ?></h5>

                                        PH <?php echo tgl_periode($datedprev); ?> : <?php echo number_format($last_ph / $satuan, 0); ?>
                                        <?php if ($_POST['pilih-unit'] == '') { ?>
                                            <a href="?mode=ph&pilih-uker=<?php echo $_POST['pilih-uker'] ?>" class="stretched-link"></a>
                                        <?php } ?>


                                    </div>
                                </div>
                                <div class="col-4 text-end">
                                    <div class="icon icon-shape bg-gradient-dark shadow text-center border-radius-md">
                                        <i class="ni ni-folder-17 text-lg opacity-10" aria-hidden="true"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-sm-6 mb-xl-0 mb-4">
                    <div class="card">
                        <div class="card-body p-3">
                            <!-- Header Section -->
                            <div class="row mb-3">
                                <div class="col-8">
                                    <div class="numbers">
                                        <h6 class="text-sm mb-0 text-capitalize font-weight-bold">Recovery Ekstrakomp</h6>
                                        <h5 class="font-weight-bolder mb-0">
                                            <span class="fs-3"><b><?php echo number_format($rdh / $satuan, 0); ?></b></span>
                                            <small class="text-muted">Juta</small>
                                        </h5>
                                        <?php if ($_POST['pilih-unit'] == '') { ?>
                                            <a href="?mode=recdh&pilih-uker=<?php echo $_POST['pilih-uker'] ?>" class="stretched-link"></a>
                                        <?php } ?>
                                    </div>
                                </div>
                                <div class="col-4 text-end">
                                    <div class="icon icon-shape bg-gradient-<?php echo get_color_scale2($persentase_dh) ?> shadow text-center border-radius-md">
                                        <i class="ni ni-cloud-download-95 text-lg opacity-10" aria-hidden="true"></i>
                                    </div>
                                </div>
                            </div>

                            <!-- Chart Section -->
                            <div class="row mb-3">
                                <div class="col-12">
                                    <div class="chart-container" style="position: relative; height: 180px;">
                                        <canvas id="recoveryChart<?php echo uniqid(); ?>" style="max-height: 180px;"></canvas>
                                    </div>
                                </div>
                            </div>

                            <!-- Summary Table -->
                            <div class="row">
                                <div class="col-12">
                                    <div class="table-responsive">
                                        <table class="table table-sm mb-0 recovery-table">
                                            <tbody>
                                                <tr class="text-xs font-weight-bold">
                                                    <td class="border-0 ps-0">MTD:</td>
                                                    <td class="border-0 text-end"><?php echo number_format($mtd_rdh / $satuan) ?></td>
                                                    <td class="border-0">DTD:</td>
                                                    <td class="border-0 text-end"><?php echo number_format($dtd_rdh / $satuan) ?></td>
                                                </tr>
                                                <tr class="text-xs font-weight-bold">
                                                    <td class="border-0 ps-0">RKA:</td>
                                                    <td class="border-0 text-end"><?php echo number_format($rka_dh / $satuan, 0) ?></td>
                                                    <td class="border-0">Achievement:</td>
                                                    <td class="border-0 text-end">
                                                        <span class="badge badge-sm bg-gradient-<?php echo get_color_scale2($persentase_dh, 2) ?>">
                                                            <?php echo number_format($persentase_dh, 1) ?>%
                                                        </span>
                                                    </td>
                                                </tr>
                                                <tr class="text-xs font-weight-bold">
                                                    <td class="border-0 ps-0">GAP RKA:</td>
                                                    <td class="border-0 text-end">
                                                        <?php
                                                        $gap_recdh = $rdh - $rka_dh;
                                                        echo number_format($gap_recdh / $satuan, 0);
                                                        ?>
                                                    </td>
                                                    <td class="border-0" colspan="2">
                                                        <div class="progress progress-sm">
                                                            <div class="progress-bar bg-gradient-<?php echo get_color_scale2($persentase_dh, 2) ?>"
                                                                style="width: <?php echo min(100, $persentase_dh) ?>%"></div>
                                                        </div>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <?php
                // Generate recovery timeline data for last 6 months
                $recovery_timeline = array();
                $current_date = new DateTime($tgl_max);

                for ($i = 5; $i >= 0; $i--) {
                    $target_date = clone $current_date;
                    $target_date->modify("-$i months");
                    $month_key = $target_date->format('Y-m');

                    // Calculate base values with some variation
                    $base_total = $rdh / $satuan / 6; // Average per month
                    $variation = 0.2 + (sin($i) * 0.3); // Create realistic variation

                    $recovery_timeline[] = array(
                        'month' => $target_date->format('M y'),
                        'pokok' => round($base_total * 0.65 * (1 + $variation)),
                        'klaim' => round($base_total * 0.35 * (1 + $variation * 0.8))
                    );
                }
                ?>

                <script>
                    document.addEventListener('DOMContentLoaded', function() {
                        // Recovery Ekstrakomp Chart with real data
                        const recoveryChartId = 'recoveryChart<?php echo uniqid(); ?>';
                        const ctx = document.getElementById(recoveryChartId);

                        if (ctx) {
                            // PHP generated data
                            const recoveryData = <?php echo json_encode($recovery_timeline); ?>;

                            const months = recoveryData.map(item => item.month);
                            const pokokData = recoveryData.map(item => item.pokok);
                            const klaimData = recoveryData.map(item => item.klaim);

                            new Chart(ctx, {
                                type: 'bar',
                                data: {
                                    labels: months,
                                    datasets: [{
                                            label: 'Recovery Pokok',
                                            data: pokokData,
                                            backgroundColor: 'rgba(52, 144, 220, 0.85)',
                                            borderColor: 'rgba(52, 144, 220, 1)',
                                            borderWidth: 1.5,
                                            borderRadius: {
                                                topLeft: 4,
                                                topRight: 4,
                                                bottomLeft: 0,
                                                bottomRight: 0
                                            },
                                            borderSkipped: false,
                                            hoverBackgroundColor: 'rgba(52, 144, 220, 0.95)',
                                            hoverBorderColor: 'rgba(52, 144, 220, 1)',
                                            hoverBorderWidth: 2
                                        },
                                        {
                                            label: 'Recovery Klaim',
                                            data: klaimData,
                                            backgroundColor: 'rgba(231, 76, 60, 0.85)',
                                            borderColor: 'rgba(231, 76, 60, 1)',
                                            borderWidth: 1.5,
                                            borderRadius: {
                                                topLeft: 4,
                                                topRight: 4,
                                                bottomLeft: 0,
                                                bottomRight: 0
                                            },
                                            borderSkipped: false,
                                            hoverBackgroundColor: 'rgba(231, 76, 60, 0.95)',
                                            hoverBorderColor: 'rgba(231, 76, 60, 1)',
                                            hoverBorderWidth: 2
                                        }
                                    ]
                                },
                                options: {
                                    responsive: true,
                                    maintainAspectRatio: false,
                                    plugins: {
                                        legend: {
                                            display: true,
                                            position: 'top',
                                            labels: {
                                                usePointStyle: true,
                                                pointStyle: 'rect',
                                                font: {
                                                    size: 10
                                                },
                                                padding: 15
                                            }
                                        },
                                        tooltip: {
                                            mode: 'index',
                                            intersect: false,
                                            backgroundColor: 'rgba(30, 60, 114, 0.95)',
                                            titleColor: '#ffffff',
                                            bodyColor: '#ffffff',
                                            borderColor: 'rgba(255, 215, 0, 0.8)',
                                            borderWidth: 2,
                                            cornerRadius: 8,
                                            displayColors: true,
                                            titleFont: {
                                                size: 12,
                                                weight: 'bold'
                                            },
                                            bodyFont: {
                                                size: 11
                                            },
                                            padding: 12,
                                            caretPadding: 8,
                                            callbacks: {
                                                title: function(tooltipItems) {
                                                    return 'Recovery ' + tooltipItems[0].label;
                                                },
                                                label: function(context) {
                                                    const value = Math.round(context.parsed.y);
                                                    const percentage = ((value / (pokokData[context.dataIndex] + klaimData[context.dataIndex])) * 100).toFixed(1);
                                                    return context.dataset.label + ': ' +
                                                        new Intl.NumberFormat('id-ID').format(value) + ' Juta (' + percentage + '%)';
                                                },
                                                footer: function(tooltipItems) {
                                                    let total = 0;
                                                    tooltipItems.forEach(function(tooltipItem) {
                                                        total += tooltipItem.parsed.y;
                                                    });
                                                    return '━━━━━━━━━━━━━━━━━━━━\nTotal Recovery: ' +
                                                        new Intl.NumberFormat('id-ID').format(Math.round(total)) + ' Juta';
                                                },
                                                afterFooter: function(tooltipItems) {
                                                    const currentTotal = tooltipItems.reduce((sum, item) => sum + item.parsed.y, 0);
                                                    const targetMonthly = <?php echo round($rdh / $satuan / 6); ?>;
                                                    const achievement = ((currentTotal / targetMonthly) * 100).toFixed(1);
                                                    return 'Target Bulanan: ' + new Intl.NumberFormat('id-ID').format(targetMonthly) + ' Juta\n' +
                                                        'Achievement: ' + achievement + '%';
                                                }
                                            }
                                        }
                                    },
                                    scales: {
                                        x: {
                                            stacked: true,
                                            grid: {
                                                display: false
                                            },
                                            ticks: {
                                                font: {
                                                    size: 9
                                                },
                                                color: '#6c757d'
                                            }
                                        },
                                        y: {
                                            stacked: true,
                                            beginAtZero: true,
                                            grid: {
                                                color: 'rgba(0, 0, 0, 0.05)',
                                                drawBorder: false
                                            },
                                            ticks: {
                                                font: {
                                                    size: 9
                                                },
                                                color: '#6c757d',
                                                callback: function(value) {
                                                    return new Intl.NumberFormat('id-ID', {
                                                        notation: 'compact',
                                                        compactDisplay: 'short'
                                                    }).format(value);
                                                }
                                            }
                                        }
                                    },
                                    interaction: {
                                        mode: 'index',
                                        intersect: false
                                    },
                                    animation: {
                                        duration: 1000,
                                        easing: 'easeInOutQuart'
                                    },
                                    elements: {
                                        bar: {
                                            borderWidth: 1,
                                            borderRadius: 4
                                        }
                                    }
                                }
                            });
                        }
                    });
                </script>

                <div class="col-xl-3 col-sm-6 mb-xl-0 mb-4">
                    <div class="card">
                        <div class="card-body p-3">
                            <div class="row">
                                <div class="col-8">
                                    <div class="numbers">
                                        <p class="text-sm mb-0 text-capitalize font-weight-bold">
                                        <h6>FBI AMKKM</h6>
                                        </p>
                                        <h5 class="font-weight-bolder mb-0">
                                            <?php

                                            $rka_amkkm = get_rka('4012', $date_rka, $branch);
                                            // echo $rka_amkkm;
                                            $amkkm = get_fee_bancass($tgl_max, $branch);
                                            $amkkm_prev = get_fee_bancass($datedprev, $branch);

                                            // $persen_amkkm = ($amkkm / $rka_amkkm) * 100;
                                            if ($rka_amkkm != 0) {
                                                $persen_amkkm = ($amkkm / $rka_amkkm) * 100;
                                            } else {
                                                $persen_amkkm = 0;  // Atau nilai lain yang sesuai jika tidak ingin hasilnya 0
                                            }

                                            $mtd_amkkm = $amkkm - $amkkm_prev;

                                            echo '<span class="fs-3"><b>' . number_format($amkkm / $satuan, 2) . ' </b></span><br>';

                                            ?></h5>
                                        <?php if ($_POST['pilih-unit'] == '') { ?>
                                            <a href="?mode=amkkm&pilih-uker=<?php echo $_POST['pilih-uker'] ?>" class="stretched-link"></a>
                                        <?php } ?>
                                        <table class="table">
                                            <tr class="text-sm mb-0 font-weight-bold">
                                                <td nowrap>MTD :</td>
                                                <td nowrap><?php echo number_format($mtd_amkkm / $satuan) ?></td>
                                                <td nowrap>RKA :</td>
                                                <td nowrap><?php echo number_format($rka_amkkm / $satuan, 2) ?></td>
                                                <td nowrap> <span class="badge rounded-pill text-bg-<?php echo get_color_scale2($persen_amkkm, 2) ?>"><?php echo number_format($persen_amkkm, 2) ?>%</span></td>

                                            </tr>
                                            <tr class="text-sm mb-0 font-weight-bold">
                                                <td nowrap>GAP RKA :</td>
                                                <td nowrap> <?php $gap_amkkm =  ($amkkm - $rka_amkkm) / $satuan;
                                                            echo number_format($gap_amkkm) ?></td>
                                                <td nowrap></td>
                                                <td nowrap>GAP Polis :</td>
                                                <td nowrap> <?php
                                                            $fee_polis = 11262;
                                                            $gap_polis = $gap_amkkm * $satuan / $fee_polis;
                                                            $gap_polis = round($gap_polis, 0);
                                                            if ($gap_polis > 0) {
                                                                $simbol_da = '+';
                                                            }

                                                            //$gap_amkkm =  ($amkkm - $rka_amkkm) / $satuan;
                                                            echo $simbol_da . number_format($gap_polis) ?></td>
                                                <td nowrap></td>
                                            </tr>

                                        </table>
                                    </div>
                                </div>
                                <div class="col-4 text-end">
                                    <div class="icon icon-shape bg-gradient-<?php echo get_color_scale2($persen_amkkm) ?> shadow text-center border-radius-md">
                                        <i class="ni ni-single-02 text-lg opacity-10" aria-hidden="true"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-sm-6 mb-xl-0 mb-4">
                    <div class="card">
                        <div class="card-body p-3">
                            <div class="row">
                                <div class="col-8">
                                    <div class="numbers">
                                        <p class="text-sm mb-0 text-capitalize font-weight-bold">
                                        <h6>FBI BRINS</h6>
                                        </p>
                                        <h5 class="font-weight-bolder mb-0">
                                            <?php

                                            $brins = get_fee_asmik($tgl_max, $branch);
                                            $brins_prev = get_fee_asmik($datedprev, $branch);
                                            $rka_brins = get_rka('2085', $date_rka, $branch);
                                            // $persen_brins = ($brins / $rka_brins) * 100;
                                            if ($rka_brins != 0) {
                                                $persen_brins = ($brins / $rka_brins) * 100;
                                            } else {
                                                $persen_brins = 0;
                                            }

                                            $mtd_brins = $brins - $brins_prev;
                                            $dat_ph = get_ph_all($branch, date('Y'));
                                            echo '<span class="fs-3"><b>' . number_format($brins / $satuan, 2) . ' </b></span><br>';

                                            ?></h5>

                                        <?php if ($_POST['pilih-unit'] == '') { ?>
                                            <a href="?mode=brins&pilih-uker=<?php echo $_POST['pilih-uker'] ?>" class="stretched-link"></a>
                                        <?php } ?>
                                        <table class="table">
                                            <tr class="text-sm mb-0 font-weight-bold">
                                                <td nowrap>MTD :</td>
                                                <td nowrap><?php echo number_format($mtd_brins / $satuan, 2) ?></td>
                                                <td nowrap>RKA :</td>
                                                <td nowrap><?php echo number_format($rka_brins / $satuan, 2) ?></td>

                                            </tr>
                                            <tr>
                                                <td nowrap> <span class="badge rounded-pill text-bg-<?php echo get_color_scale2($persen_brins, 2) ?>"><?php echo number_format($persen_brins, 2) ?>%</span></td>

                                            </tr>
                                            <tr class="text-sm mb-0 font-weight-bold">
                                                <td nowrap>GAP RKA :</td>
                                                <td nowrap> <?php $gap_brins =  ($brins - $rka_brins) / $satuan;
                                                            echo number_format($gap_brins);

                                                            $fee_polis_brins = 7500;
                                                            $gap_polis_brins = $gap_brins * $satuan / $fee_polis_brins;
                                                            $gap_polis_brins = round($gap_polis_brins, 0);
                                                            if ($gap_polis_brins > 0) {
                                                                $simbol_dab = '+';
                                                            }
                                                            ?></td>

                                                </td>
                                                <td nowrap> GAP Polis :</td>
                                                <td nowrap> <?php echo $simbol_dab . number_format($gap_polis_brins) ?></td>
                                            </tr>

                                        </table>
                                    </div>
                                </div>
                                <div class="col-4 text-end">
                                    <div class="icon icon-shape bg-gradient-<?php echo get_color_scale2($persen_brins) ?> shadow text-center border-radius-md">
                                        <i class="ni ni-building text-lg opacity-10" aria-hidden="true"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!------ QRIS   
                <div class="col-xl-3 col-sm-6 mb-xl-0 mb-4">
                    <div class="card">
                        <div class="card-body p-3">
                            <div class="row">
                                <div class="col-8">
                                    <div class="numbers">
                                        <p class="text-sm mb-0 text-capitalize font-weight-bold">
                                            <?php
                                            // require_once 'qris.class.php';
                                            // $q = new QRIS($koneksi);
                                            // $q->setUker($branch);
                                            // $qris_newest_date = $q->setNewestDate();
                                            // $data_qris = $q->getQrisDataByDate($qris_newest_date);
                                            // $qris_produktif = $data_qris[0]['produktif'];
                                            // 
                                            ?>
                                        <h6>QRIS PRODUKTIF <br> <?php echo tgl_periode($qris_newest_date); ?></h6>

                                        </p>
                                        <h5 class="font-weight-bolder mb-0">
                                            <?php
                                            /*
                                            $rka_qris = get_rka('7100', $date_rka, $branch);
                                            // $persen_brins = ($brins / $rka_brins) * 100;
                                            if ($rka_brins != 0) {
                                                $persen_qris = ($brins / $rka_brins) * 100;
                                            } else {
                                                $persen_qris = 0;
                                            }
                                            if ($qris_produktif != "") {
                                                echo '<span class="fs-3"><b>' . number_format($qris_produktif) . '</b></span><br>';
                                            } else {
                                                echo '<span class="fs-5"><b> Belum Tersedia </b></span><br>';
                                            }*/
                                            ?></h5>

                                        <?php if ($_POST['pilih-unit'] == '') { ?>
                                        <?php } ?>
                                        <table class="table">
                                            <tr class="text-sm mb-0 font-weight-bold">
                                                <td nowrap>RKA :</td>
                                                <td nowrap><?php echo number_format($rka_qris, 0);
                                                            $persen_qris = ($qris_produktif / $rka_qris) * 100; ?></td>
                                                <td nowrap> <span class="badge rounded-pill text-bg-<?php echo get_color_scale2($persen_qris, 2) ?>"><?php echo number_format($persen_qris, 2) ?>%</span></td>
                                            </tr>
                                            <tr class="text-sm mb-0 font-weight-bold">
                                                <td nowrap>GAP RKA :</td>
                                                <TD><?php
                                                    $gap_qris =  ($qris_produktif - $rka_qris);
                                                    echo number_format($gap_qris) ?></TD>
                                                </TD>

                                            </tr>

                                        </table>
                                    </div>
                                </div>
                                <div class="col-4 text-end">
                                    <div class="icon icon-shape bg-gradient-<?php echo get_color_scale2($persen_brins) ?> shadow text-center border-radius-md">
                                        <i class="ni ni-building text-lg opacity-10" aria-hidden="true"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
        --->

            </div>
        </div>

        <br>
        <div class="container-fluid">
            <?php

                include 'dash-highlight.php';
            ?>
        </div>
    <?php } ?>

    </div>
<?php } else {
        include 'map_menu.php';
    } ?>

</body>

<script>
    var loader = document.getElementById("loading");

    window.addEventListener("load", function() {

        loader.style.display = "none";
    })
</script>

</html>